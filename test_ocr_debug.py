#!/usr/bin/env python3
"""Debug OCR functionality with detailed logging"""

import logging
import sys
from PIL import Image, ImageDraw, ImageFont

# Set up detailed logging
logging.basicConfig(level=logging.DEBUG, format='%(levelname)s - %(name)s - %(message)s')

def test_ocr_with_debug():
    """Test OCR with debug information"""
    try:
        print("=== OCR Debug Test ===")
        
        # Import components
        from components.ocr_processor import MathOCRProcessor
        from components.settings_manager import SettingsManager
        
        # Initialize
        sm = SettingsManager()
        settings = sm.load_settings()
        
        # Enable debug logging for OCR
        settings['debug_mode'] = True
        
        ocr = MathOCRProcessor(settings)
        
        print(f"Tesseract available: {ocr.tesseract_processor.is_available()}")
        
        # Create a test image with clear text
        img = Image.new('RGB', (400, 200), 'white')
        draw = ImageDraw.Draw(img)
        
        # Try to use a larger, clearer font
        try:
            # Use default font but larger
            font_size = 48
            text = "x = 2 + 3"
            draw.text((50, 75), text, fill='black')
        except:
            # Fallback to basic drawing
            draw.text((50, 75), "x = 2 + 3", fill='black')
        
        # Save test image for inspection
        img.save("test_image.png")
        print("Test image saved as test_image.png")
        
        # Test OCR
        print("\n=== Testing OCR ===")
        text, conf = ocr.extract_text(img)
        
        print(f"OCR Result:")
        print(f"  Text: '{text}'")
        print(f"  Confidence: {conf:.1f}%")
        print(f"  Text length: {len(text)}")
        
        if text.strip():
            print("✅ OCR is working!")
        else:
            print("❌ OCR returned empty text")
            
            # Try direct Tesseract test
            print("\n=== Direct Tesseract Test ===")
            try:
                import pytesseract
                direct_text = pytesseract.image_to_string(img)
                print(f"Direct Tesseract result: '{direct_text.strip()}'")
            except Exception as e:
                print(f"Direct Tesseract failed: {e}")
        
    except Exception as e:
        print(f"Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_ocr_with_debug()
