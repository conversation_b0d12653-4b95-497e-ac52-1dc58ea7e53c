#!/usr/bin/env python3
"""
MathCapture Studio - Responsive Edition with Donut Integration
Version: 2.0.0
Author: MathCapture Studio Team

A modern, responsive Windows desktop application for extracting mathematical 
equations from PDFs and images using hybrid OCR (Tesseract + Donut), with 
export capabilities to Microsoft Word documents.
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import tkinter.font as tkFont
import cv2
import numpy as np
from PIL import Image, ImageTk
import pytesseract
import fitz  # PyMuPDF
from pdf2image import convert_from_path
import os
import json
import sys
import threading
import time
import io
from dataclasses import dataclass, asdict
from typing import List, Tuple, Optional, Dict, Any
import platform
import logging

# Add components to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'components'))

from ocr_processor import MathOCRProcessor, OCREngine, OCRResult
from word_exporter import WordExporter
from image_processor import ImageProcessor
from settings_manager import SettingsManager
from performance_monitor import PerformanceMonitor

@dataclass
class EquationRegion:
    """Represents a selected equation region"""
    x: int
    y: int
    width: int
    height: int
    page_num: int
    filename: str
    latex_text: str = ""
    confidence: float = 0.0
    engine_used: str = ""
    processing_time: float = 0.0

class OCRStatusWidget(ttk.Frame):
    """Widget to display OCR engine status and performance"""
    
    def __init__(self, parent, **kwargs):
        super().__init__(parent, **kwargs)
        
        # Status indicators
        self.create_status_display()
        
    def create_status_display(self):
        """Create status display elements"""
        # Engine status frame
        status_frame = ttk.LabelFrame(self, text="OCR Engine Status", padding=5)
        status_frame.pack(fill=tk.X, pady=2)
        
        # Current engine indicator
        self.current_engine_var = tk.StringVar(value="Auto")
        ttk.Label(status_frame, text="Active:").pack(side=tk.LEFT, padx=2)
        self.engine_label = ttk.Label(status_frame, textvariable=self.current_engine_var, 
                                     font=('Segoe UI', 9, 'bold'))
        self.engine_label.pack(side=tk.LEFT, padx=5)
        
        # Engine availability indicators
        self.tesseract_status = ttk.Label(status_frame, text="📊 Tesseract", foreground="green")
        self.tesseract_status.pack(side=tk.LEFT, padx=5)
        
        self.donut_status = ttk.Label(status_frame, text="🍩 Donut", foreground="gray")
        self.donut_status.pack(side=tk.LEFT, padx=5)
        
        # Performance indicators
        perf_frame = ttk.Frame(self)
        perf_frame.pack(fill=tk.X, pady=2)
        
        ttk.Label(perf_frame, text="Avg Time:").pack(side=tk.LEFT, padx=2)
        self.avg_time_var = tk.StringVar(value="N/A")
        ttk.Label(perf_frame, textvariable=self.avg_time_var).pack(side=tk.LEFT, padx=5)
        
        ttk.Label(perf_frame, text="Avg Confidence:").pack(side=tk.LEFT, padx=10)
        self.avg_confidence_var = tk.StringVar(value="N/A")
        ttk.Label(perf_frame, textvariable=self.avg_confidence_var).pack(side=tk.LEFT, padx=5)
        
    def update_status(self, engine_status: Dict[str, Any], performance_data: Dict[str, Any]):
        """Update status display"""
        # Update engine availability
        if engine_status.get('tesseract', {}).get('available', False):
            self.tesseract_status.config(foreground="green")
        else:
            self.tesseract_status.config(foreground="red")
            
        if engine_status.get('donut', {}).get('available', False):
            self.donut_status.config(foreground="green" if engine_status['donut'].get('loaded', False) else "orange")
        else:
            self.donut_status.config(foreground="red")
        
        # Update performance metrics
        real_time_stats = performance_data.get('real_time_stats', {})
        avg_time = real_time_stats.get('recent_average_time', 0)
        avg_confidence = real_time_stats.get('recent_average_confidence', 0)
        
        self.avg_time_var.set(f"{avg_time:.2f}s" if avg_time > 0 else "N/A")
        self.avg_confidence_var.set(f"{avg_confidence:.1f}%" if avg_confidence > 0 else "N/A")
    
    def set_current_engine(self, engine: str):
        """Set current active engine"""
        engine_display = {
            "tesseract": "📊 Tesseract",
            "donut": "🍩 Donut", 
            "hybrid": "🔄 Hybrid",
            "auto": "🤖 Auto"
        }
        self.current_engine_var.set(engine_display.get(engine, engine))

class MathCaptureStudioResponsive:
    """Main application class with responsive design and Donut integration"""
    
    VERSION = "2.0.0"
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title(f"MathCapture Studio v{self.VERSION} - Responsive Edition with AI")
        
        # Setup logging
        self._setup_logging()
        
        # Get screen dimensions for responsive design
        self.screen_width = self.root.winfo_screenwidth()
        self.screen_height = self.root.winfo_screenheight()
        
        # Set initial size based on screen size
        initial_width = min(1600, int(self.screen_width * 0.85))
        initial_height = min(1000, int(self.screen_height * 0.85))
        
        # Center window on screen
        x = (self.screen_width - initial_width) // 2
        y = (self.screen_height - initial_height) // 2
        
        self.root.geometry(f"{initial_width}x{initial_height}+{x}+{y}")
        self.root.minsize(1000, 700)
        
        # Configure for high DPI displays
        self._configure_dpi()
        
        # Initialize theme and styling
        self._setup_modern_theme()
        
        # Initialize managers
        self.settings_manager = SettingsManager()
        self.settings = self.settings_manager.load_settings()
        
        # Initialize performance monitor
        self.performance_monitor = PerformanceMonitor(self.settings)
        
        # Initialize OCR processor with hybrid capabilities
        self.ocr_processor = MathOCRProcessor(self.settings)
        self.word_exporter = WordExporter(self.settings)
        self.image_processor = ImageProcessor()
        
        # Application state
        self.current_files = []
        self.current_images = []
        self.current_page = 0
        self.equation_regions = []
        self.equation_queue = []
        self.selection_start = None
        self.selection_rect = None
        self.zoom_factor = 1.0
        
        # OCR processing state
        self.current_selection = None
        self.current_confidence = 0.0
        self.processing_lock = threading.Lock()
        
        # Responsive layout variables
        self.layout_mode = "desktop"  # desktop, tablet, mobile
        self.panel_sizes = {"left": 300, "right": 350, "bottom": 200}
        
        # Performance tracking
        self.last_performance_update = time.time()
        self.performance_update_interval = 5.0  # seconds
        
        self.setup_responsive_ui()
        self.check_dependencies()
        
        # Bind resize events
        self.root.bind('<Configure>', self._on_window_resize)
        
        # Start performance monitoring
        self._start_performance_monitoring()
        
        self.logger.info("MathCapture Studio initialized successfully")
        
    def _setup_logging(self):
        """Setup application logging"""
        log_level = getattr(logging, self.settings.get('log_level', 'INFO') if hasattr(self, 'settings') else 'INFO')

        # Create log directory if it doesn't exist
        log_dir = os.path.join(os.path.expanduser("~"), ".mathcapture_studio")
        os.makedirs(log_dir, exist_ok=True)
        log_file = os.path.join(log_dir, "app.log")

        logging.basicConfig(
            level=log_level,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(),
                logging.FileHandler(log_file, mode='a')
            ]
        )

        self.logger = logging.getLogger(__name__)
        
    def _configure_dpi(self):
        """Configure for high DPI displays"""
        try:
            if platform.system() == "Windows":
                from ctypes import windll
                windll.shcore.SetProcessDpiAwareness(1)
        except:
            pass
            
        # Scale factor for high DPI
        self.scale_factor = self.root.tk.call('tk', 'scaling')
        if self.scale_factor > 1.4:
            self.root.tk.call('tk', 'scaling', self.scale_factor * 0.8)
            
    def _setup_modern_theme(self):
        """Setup modern theme and styling"""
        style = ttk.Style()
        
        # Use modern theme
        available_themes = style.theme_names()
        if 'vista' in available_themes:
            style.theme_use('vista')
        elif 'clam' in available_themes:
            style.theme_use('clam')
        else:
            style.theme_use('default')
            
        # Configure modern colors
        self.colors = {
            'primary': '#2563eb',      # Blue
            'secondary': '#64748b',    # Slate
            'success': '#059669',      # Green
            'warning': '#d97706',      # Orange
            'danger': '#dc2626',       # Red
            'light': '#f8fafc',        # Light gray
            'dark': '#1e293b',         # Dark gray
            'white': '#ffffff',
            'border': '#e2e8f0',
            'donut': '#ff6b6b',        # Donut theme color
            'ai': '#8b5cf6'            # AI theme color
        }
        
        # Configure ttk styles
        style.configure('Modern.TFrame', background=self.colors['light'])
        style.configure('Sidebar.TFrame', background=self.colors['white'], relief='solid', borderwidth=1)
        style.configure('Header.TLabel', font=('Segoe UI', 12, 'bold'), background=self.colors['white'])
        style.configure('Modern.TButton', padding=(10, 5))
        style.configure('Primary.TButton', background=self.colors['primary'])
        style.configure('AI.TButton', background=self.colors['ai'])
        style.configure('Donut.TButton', background=self.colors['donut'])
        
        # Configure root background
        self.root.configure(bg=self.colors['light'])
        
    def setup_responsive_ui(self):
        """Setup the responsive UI layout"""
        self.create_modern_menu()
        self.create_responsive_toolbar()
        self.create_modern_status_bar()
        self.create_responsive_main_layout()
        
    def create_modern_menu(self):
        """Create modern application menu with OCR options"""
        menubar = tk.Menu(self.root, bg=self.colors['white'], fg=self.colors['dark'])
        self.root.config(menu=menubar)
        
        # File menu
        file_menu = tk.Menu(menubar, tearoff=0, bg=self.colors['white'], fg=self.colors['dark'])
        menubar.add_cascade(label="📁 File", menu=file_menu)
        file_menu.add_command(label="Import PDF/Images...", command=self.import_files, accelerator="Ctrl+O")
        file_menu.add_separator()
        file_menu.add_command(label="New Project", command=self.new_project, accelerator="Ctrl+N")
        file_menu.add_command(label="Save Project...", command=self.save_project, accelerator="Ctrl+S")
        file_menu.add_command(label="Load Project...", command=self.load_project, accelerator="Ctrl+L")
        file_menu.add_separator()
        file_menu.add_command(label="Export to Word...", command=self.export_to_word, accelerator="Ctrl+E")
        file_menu.add_separator()
        file_menu.add_command(label="Exit", command=self.root.quit)
        
        # OCR menu
        ocr_menu = tk.Menu(menubar, tearoff=0, bg=self.colors['white'], fg=self.colors['dark'])
        menubar.add_cascade(label="🤖 OCR", menu=ocr_menu)
        
        # Engine selection submenu
        engine_menu = tk.Menu(ocr_menu, tearoff=0)
        ocr_menu.add_cascade(label="Select Engine", menu=engine_menu)
        
        self.engine_var = tk.StringVar(value=self.settings.get('ocr_engine', 'auto'))
        engine_menu.add_radiobutton(label="🤖 Auto Select", variable=self.engine_var, 
                                   value="auto", command=self.change_ocr_engine)
        engine_menu.add_radiobutton(label="📊 Tesseract Only", variable=self.engine_var, 
                                   value="tesseract", command=self.change_ocr_engine)
        
        if self.ocr_processor.is_donut_available():
            engine_menu.add_radiobutton(label="🍩 Donut Only", variable=self.engine_var, 
                                       value="donut", command=self.change_ocr_engine)
            engine_menu.add_radiobutton(label="🔄 Hybrid Mode", variable=self.engine_var, 
                                       value="hybrid", command=self.change_ocr_engine)
        
        ocr_menu.add_separator()
        ocr_menu.add_command(label="OCR Settings...", command=self.open_ocr_settings)
        ocr_menu.add_command(label="Performance Report...", command=self.show_performance_report)
        ocr_menu.add_command(label="Clear Cache", command=self.clear_ocr_cache)
        
        # View menu
        view_menu = tk.Menu(menubar, tearoff=0, bg=self.colors['white'], fg=self.colors['dark'])
        menubar.add_cascade(label="👁️ View", menu=view_menu)
        view_menu.add_command(label="Toggle Sidebar", command=self.toggle_sidebar, accelerator="Ctrl+B")
        view_menu.add_command(label="Toggle Bottom Panel", command=self.toggle_bottom_panel, accelerator="Ctrl+J")
        view_menu.add_command(label="Toggle OCR Status", command=self.toggle_ocr_status, accelerator="Ctrl+T")
        view_menu.add_separator()
        view_menu.add_command(label="Zoom In", command=self.zoom_in, accelerator="Ctrl++")
        view_menu.add_command(label="Zoom Out", command=self.zoom_out, accelerator="Ctrl+-")
        view_menu.add_command(label="Fit to Window", command=self.fit_to_window, accelerator="Ctrl+0")
        view_menu.add_separator()
        view_menu.add_command(label="Full Screen", command=self.toggle_fullscreen, accelerator="F11")
        
        # Tools menu
        tools_menu = tk.Menu(menubar, tearoff=0, bg=self.colors['white'], fg=self.colors['dark'])
        menubar.add_cascade(label="🔧 Tools", menu=tools_menu)
        tools_menu.add_command(label="Batch Process", command=self.batch_process)
        tools_menu.add_command(label="Performance Monitor", command=self.show_performance_monitor)
        tools_menu.add_command(label="OCR Benchmark", command=self.run_ocr_benchmark)
        tools_menu.add_separator()
        tools_menu.add_command(label="Preferences", command=self.open_settings, accelerator="Ctrl+,")
        
        # Help menu
        help_menu = tk.Menu(menubar, tearoff=0, bg=self.colors['white'], fg=self.colors['dark'])
        menubar.add_cascade(label="❓ Help", menu=help_menu)
        help_menu.add_command(label="User Manual", command=self.show_user_manual)
        help_menu.add_command(label="OCR Engine Guide", command=self.show_ocr_guide)
        help_menu.add_command(label="Keyboard Shortcuts", command=self.show_shortcuts)
        help_menu.add_separator()
        help_menu.add_command(label="About", command=self.show_about)
        
        # Bind keyboard shortcuts
        self._bind_shortcuts()

    def _bind_shortcuts(self):
        """Bind keyboard shortcuts"""
        self.root.bind('<Control-o>', lambda e: self.import_files())
        self.root.bind('<Control-n>', lambda e: self.new_project())
        self.root.bind('<Control-s>', lambda e: self.save_project())
        self.root.bind('<Control-l>', lambda e: self.load_project())
        self.root.bind('<Control-e>', lambda e: self.export_to_word())
        self.root.bind('<Control-b>', lambda e: self.toggle_sidebar())
        self.root.bind('<Control-j>', lambda e: self.toggle_bottom_panel())
        self.root.bind('<Control-t>', lambda e: self.toggle_ocr_status())
        self.root.bind('<Control-plus>', lambda e: self.zoom_in())
        self.root.bind('<Control-minus>', lambda e: self.zoom_out())
        self.root.bind('<Control-0>', lambda e: self.fit_to_window())
        self.root.bind('<F11>', lambda e: self.toggle_fullscreen())
        self.root.bind('<Control-comma>', lambda e: self.open_settings())

    def create_responsive_toolbar(self):
        """Create responsive toolbar with OCR controls"""
        self.toolbar_frame = ttk.Frame(self.root, style='Modern.TFrame', padding=5)
        self.toolbar_frame.pack(side=tk.TOP, fill=tk.X)
        
        # Left section - File operations
        left_toolbar = ttk.Frame(self.toolbar_frame)
        left_toolbar.pack(side=tk.LEFT, fill=tk.Y)
        
        self.create_toolbar_button(left_toolbar, "📁", "Import Files", self.import_files, "Import PDF/Images")
        self.create_toolbar_button(left_toolbar, "💾", "Save", self.save_project, "Save Project")
        self.create_toolbar_button(left_toolbar, "📄", "Export", self.export_to_word, "Export to Word")
        
        # Separator
        ttk.Separator(left_toolbar, orient=tk.VERTICAL).pack(side=tk.LEFT, fill=tk.Y, padx=5)
        
        # OCR section
        ocr_toolbar = ttk.Frame(self.toolbar_frame)
        ocr_toolbar.pack(side=tk.LEFT, fill=tk.Y)
        
        # OCR engine selector
        ttk.Label(ocr_toolbar, text="Engine:").pack(side=tk.LEFT, padx=2)
        self.engine_combo = ttk.Combobox(ocr_toolbar, textvariable=self.engine_var, 
                                        values=["auto", "tesseract", "donut", "hybrid"], 
                                        state="readonly", width=8)
        self.engine_combo.pack(side=tk.LEFT, padx=2)
        self.engine_combo.bind('<<ComboboxSelected>>', lambda e: self.change_ocr_engine())
        
        self.create_toolbar_button(ocr_toolbar, "🔍", "OCR", self.process_selected_region, "Process OCR")
        self.create_toolbar_button(ocr_toolbar, "➕", "Add", self.add_to_queue, "Add to Queue")
        
        # Separator
        ttk.Separator(ocr_toolbar, orient=tk.VERTICAL).pack(side=tk.LEFT, fill=tk.Y, padx=5)
        
        # View operations
        view_toolbar = ttk.Frame(self.toolbar_frame)
        view_toolbar.pack(side=tk.LEFT, fill=tk.Y)
        
        self.create_toolbar_button(view_toolbar, "🔍+", "Zoom In", self.zoom_in, "Zoom In")
        self.create_toolbar_button(view_toolbar, "🔍-", "Zoom Out", self.zoom_out, "Zoom Out")
        self.create_toolbar_button(view_toolbar, "⬜", "Fit", self.fit_to_window, "Fit to Window")
        
        # Zoom level display
        self.zoom_var = tk.StringVar(value="100%")
        zoom_label = ttk.Label(view_toolbar, textvariable=self.zoom_var, font=('Segoe UI', 9))
        zoom_label.pack(side=tk.LEFT, padx=5)
        
        # Right section - Status and settings
        right_toolbar = ttk.Frame(self.toolbar_frame)
        right_toolbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # OCR status indicator
        self.ocr_status_var = tk.StringVar(value="🤖 Ready")
        self.ocr_status_label = ttk.Label(right_toolbar, textvariable=self.ocr_status_var, 
                                         font=('Segoe UI', 9, 'bold'))
        self.ocr_status_label.pack(side=tk.RIGHT, padx=5)
        
        # Layout mode indicator
        self.layout_indicator = ttk.Label(right_toolbar, text="🖥️ Desktop", font=('Segoe UI', 9))
        self.layout_indicator.pack(side=tk.RIGHT, padx=5)
        


        self.create_toolbar_button(right_toolbar, "⚙️", "Settings", self.open_settings, "Settings")

    def create_toolbar_button(self, parent, icon, text, command, tooltip=""):
        """Create a toolbar button with icon and tooltip"""
        button = ttk.Button(parent, text=icon, command=command, width=3)
        button.pack(side=tk.LEFT, padx=1)

        # Add tooltip if provided
        if tooltip:
            self._create_tooltip(button, tooltip)

        return button

    def _create_tooltip(self, widget, text):
        """Create a simple tooltip for a widget"""
        def on_enter(event):
            tooltip = tk.Toplevel()
            tooltip.wm_overrideredirect(True)
            tooltip.wm_geometry(f"+{event.x_root+10}+{event.y_root+10}")
            label = tk.Label(tooltip, text=text, background="lightyellow",
                           relief="solid", borderwidth=1, font=("Arial", 9))
            label.pack()
            widget.tooltip = tooltip

        def on_leave(event):
            if hasattr(widget, 'tooltip'):
                widget.tooltip.destroy()
                del widget.tooltip

        widget.bind("<Enter>", on_enter)
        widget.bind("<Leave>", on_leave)

    def create_modern_status_bar(self):
        """Create modern status bar"""
        self.status_frame = ttk.Frame(self.root, style='Modern.TFrame')
        self.status_frame.pack(side=tk.BOTTOM, fill=tk.X)

        # Status label
        self.status_label = ttk.Label(self.status_frame, text="Ready", font=('Segoe UI', 9))
        self.status_label.pack(side=tk.LEFT, padx=5, pady=2)

        # Progress bar
        self.progress_var = tk.IntVar()
        self.progress_bar = ttk.Progressbar(self.status_frame, variable=self.progress_var,
                                          length=200, mode='determinate')
        self.progress_bar.pack(side=tk.RIGHT, padx=5, pady=2)

    def create_responsive_main_layout(self):
        """Create the responsive main layout with OCR status panel"""
        # Main container
        self.main_container = ttk.Frame(self.root, style='Modern.TFrame')
        self.main_container.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Create responsive paned windows
        self.main_paned = ttk.PanedWindow(self.main_container, orient=tk.HORIZONTAL)
        self.main_paned.pack(fill=tk.BOTH, expand=True)
        
        # Left sidebar
        self.create_responsive_sidebar()
        
        # Center and right container
        self.center_container = ttk.Frame(self.main_paned)
        self.main_paned.add(self.center_container, weight=3)
        
        # Vertical paned window for center content
        self.vertical_paned = ttk.PanedWindow(self.center_container, orient=tk.VERTICAL)
        self.vertical_paned.pack(fill=tk.BOTH, expand=True)
        
        # Top section (preview and editor)
        self.top_container = ttk.Frame(self.vertical_paned)
        self.vertical_paned.add(self.top_container, weight=3)
        
        # Horizontal paned window for preview and editor
        self.horizontal_paned = ttk.PanedWindow(self.top_container, orient=tk.HORIZONTAL)
        self.horizontal_paned.pack(fill=tk.BOTH, expand=True)
        
        # Preview panel
        self.create_responsive_preview_panel()
        
        # Right panel container (editor + OCR status)
        self.right_container = ttk.Frame(self.horizontal_paned)
        self.horizontal_paned.add(self.right_container, weight=1)
        
        # Editor panel
        self.create_responsive_editor_panel()
        
        # OCR status panel
        self.create_ocr_status_panel()
        
        # Bottom panel (queue)
        self.create_responsive_queue_panel()
        
        # Set initial layout
        self._setup_desktop_layout()

    def create_responsive_sidebar(self):
        """Create responsive sidebar"""
        self.sidebar_frame = ttk.Frame(self.main_paned, style='Sidebar.TFrame', padding=5)
        self.main_paned.add(self.sidebar_frame, weight=1)

        # File list
        ttk.Label(self.sidebar_frame, text="📁 Files", style='Header.TLabel').pack(anchor=tk.W, pady=(0, 5))

        self.file_listbox = tk.Listbox(self.sidebar_frame, height=8)
        file_scrollbar = ttk.Scrollbar(self.sidebar_frame, orient=tk.VERTICAL, command=self.file_listbox.yview)
        self.file_listbox.config(yscrollcommand=file_scrollbar.set)

        self.file_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        file_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # Bind selection event
        self.file_listbox.bind('<<ListboxSelect>>', lambda e: self._on_file_selection_change())

    def create_responsive_preview_panel(self):
        """Create responsive preview panel"""
        self.preview_frame = ttk.LabelFrame(self.horizontal_paned, text="📄 Preview", padding=5)
        self.horizontal_paned.add(self.preview_frame, weight=2)

        # Canvas for image display
        self.canvas = tk.Canvas(self.preview_frame, bg='white')
        canvas_scrollbar_v = ttk.Scrollbar(self.preview_frame, orient=tk.VERTICAL, command=self.canvas.yview)
        canvas_scrollbar_h = ttk.Scrollbar(self.preview_frame, orient=tk.HORIZONTAL, command=self.canvas.xview)

        self.canvas.config(yscrollcommand=canvas_scrollbar_v.set, xscrollcommand=canvas_scrollbar_h.set)

        self.canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        canvas_scrollbar_v.pack(side=tk.RIGHT, fill=tk.Y)
        canvas_scrollbar_h.pack(side=tk.BOTTOM, fill=tk.X)

        # Bind mouse events for selection
        self.canvas.bind("<Button-1>", self.on_canvas_click)
        self.canvas.bind("<B1-Motion>", self.on_canvas_drag)
        self.canvas.bind("<ButtonRelease-1>", self.on_canvas_release)

    def create_responsive_editor_panel(self):
        """Create responsive editor panel"""
        self.editor_frame = ttk.LabelFrame(self.right_container, text="✏️ Editor", padding=5)
        self.editor_frame.pack(fill=tk.BOTH, expand=True)

        # OCR text area
        ttk.Label(self.editor_frame, text="OCR Text:").pack(anchor=tk.W)
        self.ocr_text = tk.Text(self.editor_frame, height=6, wrap=tk.WORD)
        ocr_scrollbar = ttk.Scrollbar(self.editor_frame, orient=tk.VERTICAL, command=self.ocr_text.yview)
        self.ocr_text.config(yscrollcommand=ocr_scrollbar.set)

        ocr_frame = ttk.Frame(self.editor_frame)
        ocr_frame.pack(fill=tk.X, pady=(0, 5))
        self.ocr_text.pack(in_=ocr_frame, side=tk.LEFT, fill=tk.BOTH, expand=True)
        ocr_scrollbar.pack(in_=ocr_frame, side=tk.RIGHT, fill=tk.Y)

        # LaTeX text area
        ttk.Label(self.editor_frame, text="LaTeX:").pack(anchor=tk.W)
        self.latex_text = tk.Text(self.editor_frame, height=6, wrap=tk.WORD)
        latex_scrollbar = ttk.Scrollbar(self.editor_frame, orient=tk.VERTICAL, command=self.latex_text.yview)
        self.latex_text.config(yscrollcommand=latex_scrollbar.set)

        latex_frame = ttk.Frame(self.editor_frame)
        latex_frame.pack(fill=tk.X, pady=(0, 5))
        self.latex_text.pack(in_=latex_frame, side=tk.LEFT, fill=tk.BOTH, expand=True)
        latex_scrollbar.pack(in_=latex_frame, side=tk.RIGHT, fill=tk.Y)

        # Confidence indicator
        confidence_frame = ttk.Frame(self.editor_frame)
        confidence_frame.pack(fill=tk.X, pady=(5, 0))

        self.confidence_var = tk.StringVar(value="Confidence: N/A")
        ttk.Label(confidence_frame, textvariable=self.confidence_var).pack(side=tk.LEFT)

        self.confidence_progress = ttk.Progressbar(confidence_frame, length=100, mode='determinate')
        self.confidence_progress.pack(side=tk.RIGHT, padx=(10, 0))

    def create_responsive_queue_panel(self):
        """Create responsive queue panel"""
        self.queue_frame = ttk.LabelFrame(self.vertical_paned, text="📋 Equation Queue", padding=5)
        self.vertical_paned.add(self.queue_frame, weight=1)

        # Queue listbox
        self.queue_listbox = tk.Listbox(self.queue_frame, height=4)
        queue_scrollbar = ttk.Scrollbar(self.queue_frame, orient=tk.VERTICAL, command=self.queue_listbox.yview)
        self.queue_listbox.config(yscrollcommand=queue_scrollbar.set)

        self.queue_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        queue_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    def _setup_desktop_layout(self):
        """Setup desktop layout configuration"""
        self.layout_mode = "desktop"
        self.layout_indicator.config(text="🖥️ Desktop")

        # Set paned window positions
        self.main_paned.sashpos(0, self.panel_sizes["left"])
        self.horizontal_paned.sashpos(0, 600)  # Preview panel width
        self.vertical_paned.sashpos(0, 400)    # Top section height

    def create_ocr_status_panel(self):
        """Create OCR status and performance panel"""
        self.ocr_status_frame = ttk.LabelFrame(self.right_container, text="🤖 OCR Status", padding=5)
        self.ocr_status_frame.pack(fill=tk.X, pady=(5, 0))
        
        # Create OCR status widget
        self.ocr_status_widget = OCRStatusWidget(self.ocr_status_frame)
        self.ocr_status_widget.pack(fill=tk.X)
        
        # Quick actions
        actions_frame = ttk.Frame(self.ocr_status_frame)
        actions_frame.pack(fill=tk.X, pady=(5, 0))
        
        ttk.Button(actions_frame, text="📊 Report", command=self.show_performance_report, 
                  width=8).pack(side=tk.LEFT, padx=2)
        ttk.Button(actions_frame, text="🧹 Clear", command=self.clear_ocr_cache, 
                  width=8).pack(side=tk.LEFT, padx=2)
        ttk.Button(actions_frame, text="⚙️ Settings", command=self.open_ocr_settings, 
                  width=8).pack(side=tk.LEFT, padx=2)
        
    def _start_performance_monitoring(self):
        """Start background performance monitoring"""
        def update_performance():
            while True:
                try:
                    time.sleep(self.performance_update_interval)
                    
                    # Get current performance data
                    engine_status = self.ocr_processor.get_engine_status()
                    performance_data = {
                        'real_time_stats': self.performance_monitor.get_real_time_stats()
                    }
                    
                    # Update UI in main thread
                    self.root.after(0, self._update_performance_ui, engine_status, performance_data)
                    
                except Exception as e:
                    self.logger.error(f"Performance monitoring error: {e}")
                    time.sleep(10)  # Wait longer on error
        
        # Start monitoring thread
        monitor_thread = threading.Thread(target=update_performance, daemon=True)
        monitor_thread.start()
        
    def _update_performance_ui(self, engine_status: Dict[str, Any], performance_data: Dict[str, Any]):
        """Update performance UI elements"""
        try:
            # Update OCR status widget
            if hasattr(self, 'ocr_status_widget'):
                self.ocr_status_widget.update_status(engine_status, performance_data)
            
            # Update toolbar status
            current_engine = self.settings.get('ocr_engine', 'auto')
            engine_icons = {
                'auto': '🤖 Auto',
                'tesseract': '📊 Tesseract', 
                'donut': '🍩 Donut',
                'hybrid': '🔄 Hybrid'
            }
            
            status_text = engine_icons.get(current_engine, current_engine)
            
            # Add processing indicator if busy
            if hasattr(self, 'processing_lock') and self.processing_lock.locked():
                status_text += " ⏳"
            
            self.ocr_status_var.set(status_text)
            
        except Exception as e:
            self.logger.error(f"Performance UI update error: {e}")
    
    def change_ocr_engine(self):
        """Change OCR engine"""
        new_engine = self.ocr_engine_var.get()
        self.settings['ocr_engine'] = new_engine
        self.settings_manager.save_settings(self.settings)

        # Update OCR status
        if hasattr(self, 'ocr_status_widget'):
            self.ocr_status_widget.set_current_engine(new_engine)

        self.update_status(f"OCR engine changed to: {new_engine}")
        self.logger.info(f"OCR engine changed to: {new_engine}")

    def process_ocr(self):
        """Main OCR processing function called by toolbar button"""
        try:
            # Check if we have a selection
            if not hasattr(self, 'current_selection') or not self.current_selection:
                messagebox.showwarning("No Selection", "Please select a region on the image first by clicking and dragging.")
                return

            # Check if we have an image loaded
            if not hasattr(self, 'current_images') or not self.current_images:
                messagebox.showwarning("No Image", "Please import and select an image file first.")
                return

            # Check if already processing
            if hasattr(self, 'processing_lock') and self.processing_lock.locked():
                messagebox.showinfo("Processing", "OCR processing is already in progress. Please wait.")
                return

            # Start OCR processing
            self.process_selected_region()

        except Exception as e:
            self.logger.error(f"OCR processing failed: {e}")
            messagebox.showerror("OCR Error", f"Failed to start OCR processing: {str(e)}")
    
    def process_selected_region(self):
        """Process the selected region with OCR using current engine"""
        if not hasattr(self, 'current_selection') or not self.current_images:
            messagebox.showwarning("Warning", "Please select a region first.")
            return
        
        # Check if already processing
        if self.processing_lock.locked():
            messagebox.showinfo("Processing", "OCR processing is already in progress.")
            return
            
        self.update_status("Processing OCR...")
        self.progress_var.set(25)
        
        # Get current image and selection
        img_data = self.current_images[self.current_page]
        img = img_data['image']
        sel = self.current_selection
        
        # Crop the selected region
        try:
            cropped = img.crop((sel['x'], sel['y'], 
                              sel['x'] + sel['width'], 
                              sel['y'] + sel['height']))
            
            # Run OCR in a separate thread
            threading.Thread(target=self.run_ocr_async, args=(cropped, img_data), daemon=True).start()
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to process region: {str(e)}")
            self.progress_var.set(0)
            self.update_status("Ready")
    
    def run_ocr_async(self, cropped_img: Image.Image, img_data: Dict[str, Any]):
        """Run OCR asynchronously with performance monitoring"""
        with self.processing_lock:
            start_time = time.time()

            try:
                # Get current engine setting
                engine_setting = self.ocr_engine_var.get()

                # Create OCREngine enum if not auto
                engine = None
                if engine_setting != 'auto':
                    try:
                        engine = OCREngine(engine_setting.upper())
                    except ValueError:
                        engine = None

                # Process with OCR processor
                text, confidence = self.ocr_processor.extract_text(cropped_img, engine)

                processing_time = time.time() - start_time

                # Record performance metrics
                self.performance_monitor.record_event(
                    engine=engine_setting,
                    processing_time=processing_time,
                    confidence=confidence,
                    success=len(text.strip()) > 0,
                    image_size=cropped_img.size,
                    complexity_score=0.0,  # Would be calculated by complexity analyzer
                    text_length=len(text),
                    error_message=""
                )

                # Update UI in main thread
                self.root.after(0, self.update_ocr_result, text, confidence, processing_time, engine_setting)

            except Exception as e:
                processing_time = time.time() - start_time
                error_msg = str(e)

                # Record failed attempt
                self.performance_monitor.record_event(
                    engine=engine_setting,
                    processing_time=processing_time,
                    confidence=0.0,
                    success=False,
                    image_size=cropped_img.size,
                    complexity_score=0.0,
                    text_length=0,
                    error_message=error_msg
                )

                self.root.after(0, lambda: messagebox.showerror("OCR Error", error_msg))
                self.root.after(0, lambda: self.progress_var.set(0))
                self.root.after(0, lambda: self.update_status("OCR failed"))
    
    def update_ocr_result(self, text: str, confidence: float, processing_time: float, engine_used: str):
        """Update OCR result in the UI"""
        self.ocr_text.delete(1.0, tk.END)
        self.ocr_text.insert(1.0, text)
        
        # Convert to LaTeX
        latex_text = self.ocr_processor.convert_to_latex(text)
        self.latex_text.delete(1.0, tk.END)
        self.latex_text.insert(1.0, latex_text)
        
        # Update confidence and status
        self.current_confidence = confidence
        self.confidence_var.set(f"Confidence: {confidence:.1f}%")
        self.confidence_progress.config(value=confidence)
        
        # Update processing info
        engine_icons = {'tesseract': '📊', 'donut': '🍩', 'hybrid': '🔄', 'auto': '🤖'}
        engine_icon = engine_icons.get(engine_used, '🔍')
        
        self.progress_var.set(100)
        self.update_status(f"{engine_icon} OCR completed in {processing_time:.2f}s with {confidence:.1f}% confidence")
        
        # Reset progress after a delay
        self.root.after(2000, lambda: self.progress_var.set(0))
        
        self.logger.info(f"OCR completed: {engine_used}, {processing_time:.2f}s, {confidence:.1f}%")
    
    # OCR-related menu actions
    def open_ocr_settings(self):
        """Open OCR settings dialog"""
        OCRSettingsDialog(self.root, self.settings, self.settings_manager, self.ocr_processor)
    
    def show_performance_report(self):
        """Show performance report dialog"""
        PerformanceReportDialog(self.root, self.performance_monitor)
    
    def show_performance_monitor(self):
        """Show real-time performance monitor"""
        PerformanceMonitorDialog(self.root, self.performance_monitor, self.ocr_processor)
    
    def clear_ocr_cache(self):
        """Clear OCR result cache"""
        self.ocr_processor.clear_cache()
        messagebox.showinfo("Cache Cleared", "OCR result cache has been cleared.")
        self.update_status("OCR cache cleared")
    
    def run_ocr_benchmark(self):
        """Run OCR benchmark test"""
        BenchmarkDialog(self.root, self.ocr_processor, self.performance_monitor)
    
    def toggle_ocr_status(self):
        """Toggle OCR status panel visibility"""
        if hasattr(self, 'ocr_status_frame'):
            if self.ocr_status_frame.winfo_viewable():
                self.ocr_status_frame.pack_forget()
            else:
                self.ocr_status_frame.pack(fill=tk.X, pady=(5, 0))
    
    def show_ocr_guide(self):
        """Show OCR engine guide"""
        guide_text = f"""
OCR Engine Guide - MathCapture Studio v{self.VERSION}

🤖 AUTO MODE (Recommended)
- Automatically selects the best engine based on content complexity
- Uses Tesseract for simple text and Donut for complex mathematics
- Provides optimal balance of speed and accuracy

📊 TESSERACT ENGINE
- Fast and lightweight traditional OCR
- Best for: Simple equations, typed text, quick processing
- Average processing time: 1-3 seconds
- Memory usage: Low (< 100MB)

🍩 DONUT ENGINE (AI-Powered)
- Advanced transformer-based document understanding
- Best for: Complex equations, handwritten math, scientific notation
- Average processing time: 5-15 seconds
- Memory usage: High (2-4GB)
- Requires: Modern hardware, preferably with GPU

🔄 HYBRID MODE
- Tries both engines and selects the best result
- Automatically falls back if one engine fails
- Best for: Mixed content with varying complexity
- Processing time: Variable (2-20 seconds)

PERFORMANCE TIPS:
• Use GPU acceleration for faster Donut processing
• Enable result caching to avoid reprocessing identical regions
• Use Tesseract for batch processing of simple content
• Monitor performance metrics to optimize settings

TROUBLESHOOTING:
• If Donut is slow: Enable GPU or reduce image size
• If accuracy is low: Try different engines or preprocess images
• If processing fails: Check system requirements and dependencies
        """
        
        dialog = tk.Toplevel(self.root)
        dialog.title("OCR Engine Guide")
        dialog.geometry("600x500")
        dialog.transient(self.root)
        
        text_widget = tk.Text(dialog, wrap=tk.WORD, font=('Consolas', 10))
        scrollbar = ttk.Scrollbar(dialog, orient=tk.VERTICAL, command=text_widget.yview)
        text_widget.config(yscrollcommand=scrollbar.set)
        
        text_widget.insert(1.0, guide_text)
        text_widget.config(state=tk.DISABLED)
        
        text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    # Include all the other methods from the previous implementation
    # (I'll include the key ones here for brevity)
    
    def check_dependencies(self):
        """Check if all required dependencies are available"""
        missing_deps = []
        warnings = []

        # Check Tesseract
        if not self.ocr_processor.tesseract_processor.is_available():
            missing_deps.append("Tesseract OCR")

        # Check Donut availability
        if not self.ocr_processor.is_donut_available():
            warnings.append("Donut AI engine not available - install PyTorch and Transformers for enhanced accuracy")

        # Check other critical dependencies
        try:
            import cv2
        except ImportError:
            missing_deps.append("OpenCV")

        try:
            import fitz
        except ImportError:
            missing_deps.append("PyMuPDF")

        # Show messages
        if missing_deps:
            msg = f"Missing critical dependencies: {', '.join(missing_deps)}\n\n"
            if "Tesseract OCR" in missing_deps:
                msg += "To install Tesseract OCR:\n"
                msg += "1. Download from: https://github.com/UB-Mannheim/tesseract/wiki\n"
                msg += "2. Install and add to your system PATH\n"
                msg += "3. Restart the application\n\n"
            msg += "Please install missing dependencies for full functionality."
            messagebox.showerror("Missing Dependencies", msg)
        elif warnings:
            msg = f"Optional features unavailable:\n\n{chr(10).join(warnings)}\n\n"
            msg += "The application will work with reduced functionality."
            messagebox.showwarning("Optional Dependencies", msg)
        else:
            self.logger.info("All dependencies available")
    
    # Mouse event handlers for canvas selection
    def on_canvas_click(self, event):
        """Handle canvas click for selection start"""
        self.selection_start = (self.canvas.canvasx(event.x), self.canvas.canvasy(event.y))
        if hasattr(self, 'selection_rect') and self.selection_rect:
            self.canvas.delete(self.selection_rect)

    def on_canvas_drag(self, event):
        """Handle canvas drag for selection"""
        if self.selection_start:
            current_pos = (self.canvas.canvasx(event.x), self.canvas.canvasy(event.y))
            if hasattr(self, 'selection_rect') and self.selection_rect:
                self.canvas.delete(self.selection_rect)

            self.selection_rect = self.canvas.create_rectangle(
                self.selection_start[0], self.selection_start[1],
                current_pos[0], current_pos[1],
                outline='red', width=2
            )

    def on_canvas_release(self, event):
        """Handle canvas release to finalize selection"""
        if self.selection_start:
            end_pos = (self.canvas.canvasx(event.x), self.canvas.canvasy(event.y))

            # Calculate selection bounds in canvas coordinates
            x1, y1 = self.selection_start
            x2, y2 = end_pos

            # Convert canvas coordinates to image coordinates (accounting for zoom)
            img_x1 = int(min(x1, x2) / self.zoom_factor)
            img_y1 = int(min(y1, y2) / self.zoom_factor)
            img_width = int(abs(x2 - x1) / self.zoom_factor)
            img_height = int(abs(y2 - y1) / self.zoom_factor)

            self.current_selection = {
                'x': img_x1,
                'y': img_y1,
                'width': img_width,
                'height': img_height
            }

            self.update_status(f"Selected region: {img_width}x{img_height} at ({img_x1}, {img_y1})")

    def _on_window_resize(self, event):
        """Handle window resize for responsive layout"""
        if event.widget == self.root:
            # Update layout mode based on window size
            width = self.root.winfo_width()
            if width < 1200:
                if self.layout_mode != "tablet":
                    self._setup_tablet_layout()
            elif width < 800:
                if self.layout_mode != "mobile":
                    self._setup_mobile_layout()
            else:
                if self.layout_mode != "desktop":
                    self._setup_desktop_layout()

    def _setup_tablet_layout(self):
        """Setup tablet layout"""
        self.layout_mode = "tablet"
        self.layout_indicator.config(text="📱 Tablet")

    def _setup_mobile_layout(self):
        """Setup mobile layout"""
        self.layout_mode = "mobile"
        self.layout_indicator.config(text="📱 Mobile")

    # View control methods
    def zoom_in(self):
        """Zoom in on the preview"""
        self.zoom_factor *= 1.2
        self.zoom_var.set(f"{int(self.zoom_factor * 100)}%")
        self._display_current_page()  # Refresh display with new zoom
        self.update_status("Zoomed in")

    def zoom_out(self):
        """Zoom out on the preview"""
        self.zoom_factor /= 1.2
        if self.zoom_factor < 0.1:  # Prevent zooming out too much
            self.zoom_factor = 0.1
        self.zoom_var.set(f"{int(self.zoom_factor * 100)}%")
        self._display_current_page()  # Refresh display with new zoom
        self.update_status("Zoomed out")

    def fit_to_window(self):
        """Fit image to window"""
        if hasattr(self, 'current_images') and self.current_images and self.current_page < len(self.current_images):
            # Calculate zoom to fit image in canvas
            image = self.current_images[self.current_page]['image']
            canvas_width = self.canvas.winfo_width()
            canvas_height = self.canvas.winfo_height()

            if canvas_width > 1 and canvas_height > 1:  # Make sure canvas is initialized
                zoom_x = canvas_width / image.width
                zoom_y = canvas_height / image.height
                self.zoom_factor = min(zoom_x, zoom_y) * 0.9  # 90% to leave some margin
            else:
                self.zoom_factor = 1.0
        else:
            self.zoom_factor = 1.0

        self.zoom_var.set(f"{int(self.zoom_factor * 100)}%")
        self._display_current_page()  # Refresh display with new zoom
        self.update_status("Fit to window")

    def _display_current_page(self):
        """Display the current page with current zoom factor"""
        try:
            if not hasattr(self, 'current_images') or not self.current_images:
                return

            if self.current_page >= len(self.current_images):
                return

            # Get current image
            img_data = self.current_images[self.current_page]
            img = img_data['image']

            # Apply zoom
            new_width = int(img.width * self.zoom_factor)
            new_height = int(img.height * self.zoom_factor)

            if new_width > 0 and new_height > 0:
                # Resize image
                resized_img = img.resize((new_width, new_height), Image.Resampling.LANCZOS)

                # Convert to PhotoImage
                self.current_photo = ImageTk.PhotoImage(resized_img)

                # Clear canvas and display image
                self.canvas.delete("all")
                self.canvas.create_image(0, 0, anchor=tk.NW, image=self.current_photo)

                # Update scroll region
                self.canvas.configure(scrollregion=self.canvas.bbox("all"))

                # Redraw selection if exists
                if hasattr(self, 'current_selection') and self.current_selection:
                    self._draw_selection_rectangle()

        except Exception as e:
            self.logger.error(f"Failed to display current page: {e}")

    def _draw_selection_rectangle(self):
        """Draw selection rectangle on canvas"""
        try:
            if hasattr(self, 'current_selection') and self.current_selection:
                sel = self.current_selection
                # Scale selection coordinates with zoom
                x1 = sel['x'] * self.zoom_factor
                y1 = sel['y'] * self.zoom_factor
                x2 = (sel['x'] + sel['width']) * self.zoom_factor
                y2 = (sel['y'] + sel['height']) * self.zoom_factor

                # Draw rectangle
                self.canvas.create_rectangle(x1, y1, x2, y2,
                                           outline='red', width=2, tags='selection')
        except Exception as e:
            self.logger.error(f"Failed to draw selection rectangle: {e}")

    # Toggle methods
    def toggle_sidebar(self):
        """Toggle sidebar visibility"""
        if hasattr(self, 'sidebar_frame'):
            if self.sidebar_frame.winfo_viewable():
                self.sidebar_frame.pack_forget()
            else:
                self.main_paned.add(self.sidebar_frame, weight=1)

    def toggle_bottom_panel(self):
        """Toggle bottom panel visibility"""
        if hasattr(self, 'queue_frame'):
            if self.queue_frame.winfo_viewable():
                self.queue_frame.pack_forget()
            else:
                self.vertical_paned.add(self.queue_frame, weight=1)

    def toggle_fullscreen(self):
        """Toggle fullscreen mode"""
        current_state = self.root.attributes('-fullscreen')
        self.root.attributes('-fullscreen', not current_state)

    # File management methods
    def import_files(self):
        """Import PDF files or images"""
        try:
            # File dialog for selecting files
            filetypes = [
                ("All supported", "*.pdf;*.png;*.jpg;*.jpeg;*.bmp;*.tiff;*.tif"),
                ("PDF files", "*.pdf"),
                ("Image files", "*.png;*.jpg;*.jpeg;*.bmp;*.tiff;*.tif"),
                ("All files", "*.*")
            ]

            filenames = filedialog.askopenfilenames(
                title="Import PDF/Image Files",
                filetypes=filetypes
            )

            if not filenames:
                return

            self.update_status("Loading files...")
            self.progress_var.set(10)

            # Process files in a separate thread to avoid blocking UI
            threading.Thread(target=self._load_files_async, args=(filenames,), daemon=True).start()

        except Exception as e:
            messagebox.showerror("Import Error", f"Failed to import files: {str(e)}")
            self.progress_var.set(0)
            self.update_status("Ready")

    def _load_files_async(self, filenames):
        """Load files asynchronously"""
        try:
            total_files = len(filenames)
            loaded_files = []

            for i, filename in enumerate(filenames):
                try:
                    # Update progress
                    progress = 10 + (i / total_files) * 80
                    self.root.after(0, lambda p=progress: self.progress_var.set(p))
                    self.root.after(0, lambda f=filename: self.update_status(f"Loading {os.path.basename(f)}..."))

                    # Load file based on type
                    if filename.lower().endswith('.pdf'):
                        file_data = self._load_pdf_file(filename)
                    else:
                        file_data = self._load_image_file(filename)

                    if file_data:
                        loaded_files.append(file_data)

                except Exception as e:
                    self.logger.error(f"Failed to load {filename}: {e}")
                    self.root.after(0, lambda f=filename, err=str(e):
                                  messagebox.showwarning("Load Warning", f"Failed to load {os.path.basename(f)}: {err}"))

            # Update UI in main thread
            self.root.after(0, self._update_file_list, loaded_files)
            self.root.after(0, lambda: self.progress_var.set(100))
            self.root.after(0, lambda: self.update_status(f"Loaded {len(loaded_files)} files"))
            self.root.after(2000, lambda: self.progress_var.set(0))

        except Exception as e:
            self.logger.error(f"File loading failed: {e}")
            self.root.after(0, lambda: messagebox.showerror("Load Error", f"File loading failed: {str(e)}"))
            self.root.after(0, lambda: self.progress_var.set(0))
            self.root.after(0, lambda: self.update_status("Ready"))

    def _load_pdf_file(self, filename):
        """Load PDF file and convert pages to images"""
        try:
            # Open PDF with PyMuPDF
            pdf_doc = fitz.open(filename)
            pages = []

            for page_num in range(len(pdf_doc)):
                page = pdf_doc[page_num]

                # Convert page to image
                mat = fitz.Matrix(2.0, 2.0)  # 2x zoom for better quality
                pix = page.get_pixmap(matrix=mat)
                img_data = pix.tobytes("png")

                # Convert to PIL Image
                image = Image.open(io.BytesIO(img_data))

                pages.append({
                    'page_num': page_num,
                    'image': image,
                    'thumbnail': self._create_thumbnail(image),
                    'size': image.size
                })

            pdf_doc.close()

            return {
                'filename': filename,
                'basename': os.path.basename(filename),
                'type': 'pdf',
                'pages': pages,
                'total_pages': len(pages),
                'current_page': 0
            }

        except Exception as e:
            self.logger.error(f"Failed to load PDF {filename}: {e}")
            raise

    def _load_image_file(self, filename):
        """Load image file"""
        try:
            # Open image with PIL
            image = Image.open(filename)

            # Convert to RGB if necessary
            if image.mode not in ('RGB', 'L'):
                image = image.convert('RGB')

            pages = [{
                'page_num': 0,
                'image': image,
                'thumbnail': self._create_thumbnail(image),
                'size': image.size
            }]

            return {
                'filename': filename,
                'basename': os.path.basename(filename),
                'type': 'image',
                'pages': pages,
                'total_pages': 1,
                'current_page': 0
            }

        except Exception as e:
            self.logger.error(f"Failed to load image {filename}: {e}")
            raise

    def _create_thumbnail(self, image, size=(150, 200)):
        """Create thumbnail for image"""
        try:
            thumbnail = image.copy()
            thumbnail.thumbnail(size, Image.Resampling.LANCZOS)
            return thumbnail
        except:
            return image

    def _update_file_list(self, loaded_files):
        """Update file list in UI"""
        # Add to current files
        self.current_files.extend(loaded_files)

        # Update file listbox
        self.file_listbox.delete(0, tk.END)
        for i, file_data in enumerate(self.current_files):
            display_name = f"{file_data['basename']} ({file_data['total_pages']} pages)"
            self.file_listbox.insert(tk.END, display_name)

        # Select first file if none selected
        if self.current_files and self.file_listbox.size() > 0:
            self.file_listbox.selection_set(0)
            self._on_file_selection_change()

    def _on_file_selection_change(self):
        """Handle file selection change"""
        try:
            selection = self.file_listbox.curselection()
            if not selection:
                return

            file_index = selection[0]
            if 0 <= file_index < len(self.current_files):
                file_data = self.current_files[file_index]
                self.current_images = file_data['pages']
                self.current_page = 0
                self._display_current_page()

        except Exception as e:
            self.logger.error(f"File selection change failed: {e}")

    def new_project(self):
        messagebox.showinfo("New Project", "New project functionality")

    def save_project(self):
        messagebox.showinfo("Save Project", "Save project functionality")

    def load_project(self):
        messagebox.showinfo("Load Project", "Load project functionality")

    def export_to_word(self):
        messagebox.showinfo("Export", "Export to Word functionality")

    def add_to_queue(self):
        """Add current OCR result to equation queue"""
        try:
            # Check if we have OCR results to add
            if not hasattr(self, 'ocr_text') or not self.ocr_text.get(1.0, tk.END).strip():
                messagebox.showwarning("No Content", "Please process an equation with OCR first.")
                return

            if not hasattr(self, 'current_selection') or not self.current_selection:
                messagebox.showwarning("No Selection", "Please select a region first.")
                return

            # Get current OCR results
            ocr_text = self.ocr_text.get(1.0, tk.END).strip()
            latex_text = self.latex_text.get(1.0, tk.END).strip()
            confidence = getattr(self, 'current_confidence', 0.0)

            # Create equation region object
            equation = EquationRegion(
                x=self.current_selection['x'],
                y=self.current_selection['y'],
                width=self.current_selection['width'],
                height=self.current_selection['height'],
                page_num=self.current_page,
                filename=self.current_files[0]['basename'] if self.current_files else "Unknown",
                latex_text=latex_text,
                confidence=confidence,
                engine_used=self.settings.get('ocr_engine', 'auto'),
                processing_time=0.0  # Would be set during processing
            )

            # Add to queue
            self.equation_regions.append(equation)

            # Update queue display
            self._update_queue_display()

            # Clear current selection
            if hasattr(self, 'selection_rect') and self.selection_rect:
                self.canvas.delete(self.selection_rect)
                self.selection_rect = None
            self.current_selection = None

            # Clear OCR results
            self.ocr_text.delete(1.0, tk.END)
            self.latex_text.delete(1.0, tk.END)
            self.confidence_var.set("Confidence: N/A")
            self.confidence_progress.config(value=0)

            self.update_status(f"Added equation to queue. Total: {len(self.equation_regions)}")

        except Exception as e:
            self.logger.error(f"Failed to add to queue: {e}")
            messagebox.showerror("Queue Error", f"Failed to add equation to queue: {str(e)}")

    def _update_queue_display(self):
        """Update the equation queue display"""
        try:
            # Clear current queue display
            self.queue_listbox.delete(0, tk.END)

            # Add equations to display
            for i, equation in enumerate(self.equation_regions):
                # Create display text
                display_text = f"Eq {i+1}: {equation.filename} (Page {equation.page_num + 1})"
                if equation.latex_text:
                    preview = equation.latex_text[:50] + "..." if len(equation.latex_text) > 50 else equation.latex_text
                    display_text += f" - {preview}"

                self.queue_listbox.insert(tk.END, display_text)

            # Bind events for queue management
            self.queue_listbox.bind('<Double-Button-1>', self._on_queue_double_click)
            self.queue_listbox.bind('<Button-3>', self._on_queue_right_click)  # Right-click context menu

        except Exception as e:
            self.logger.error(f"Failed to update queue display: {e}")

    def _on_queue_double_click(self, event):
        """Handle double-click on queue item to edit"""
        try:
            selection = self.queue_listbox.curselection()
            if not selection:
                return

            equation_index = selection[0]
            if 0 <= equation_index < len(self.equation_regions):
                equation = self.equation_regions[equation_index]
                self._edit_equation(equation_index, equation)

        except Exception as e:
            self.logger.error(f"Queue double-click failed: {e}")

    def _on_queue_right_click(self, event):
        """Handle right-click on queue item for context menu"""
        try:
            # Select the item under cursor
            index = self.queue_listbox.nearest(event.y)
            self.queue_listbox.selection_clear(0, tk.END)
            self.queue_listbox.selection_set(index)

            # Create context menu
            context_menu = tk.Menu(self.root, tearoff=0)
            context_menu.add_command(label="Edit", command=lambda: self._edit_selected_equation())
            context_menu.add_command(label="Delete", command=lambda: self._delete_selected_equation())
            context_menu.add_separator()
            context_menu.add_command(label="Move Up", command=lambda: self._move_equation_up())
            context_menu.add_command(label="Move Down", command=lambda: self._move_equation_down())
            context_menu.add_separator()
            context_menu.add_command(label="Export This", command=lambda: self._export_selected_equation())

            # Show context menu
            context_menu.tk_popup(event.x_root, event.y_root)

        except Exception as e:
            self.logger.error(f"Queue right-click failed: {e}")

    def _edit_selected_equation(self):
        """Edit the selected equation"""
        try:
            selection = self.queue_listbox.curselection()
            if not selection:
                return

            equation_index = selection[0]
            if 0 <= equation_index < len(self.equation_regions):
                equation = self.equation_regions[equation_index]
                self._edit_equation(equation_index, equation)

        except Exception as e:
            self.logger.error(f"Edit equation failed: {e}")

    def _edit_equation(self, index, equation):
        """Open equation editor dialog"""
        EquationEditorDialog(self.root, equation, lambda updated_eq: self._update_equation(index, updated_eq))

    def _update_equation(self, index, updated_equation):
        """Update equation in queue"""
        try:
            if 0 <= index < len(self.equation_regions):
                self.equation_regions[index] = updated_equation
                self._update_queue_display()
                self.update_status(f"Updated equation {index + 1}")

        except Exception as e:
            self.logger.error(f"Update equation failed: {e}")

    def _delete_selected_equation(self):
        """Delete the selected equation"""
        try:
            selection = self.queue_listbox.curselection()
            if not selection:
                return

            equation_index = selection[0]
            if 0 <= equation_index < len(self.equation_regions):
                equation = self.equation_regions[equation_index]

                if messagebox.askyesno("Delete Equation", f"Delete equation {equation_index + 1}?"):
                    del self.equation_regions[equation_index]
                    self._update_queue_display()
                    self.update_status(f"Deleted equation. Total: {len(self.equation_regions)}")

        except Exception as e:
            self.logger.error(f"Delete equation failed: {e}")

    def _move_equation_up(self):
        """Move selected equation up in queue"""
        try:
            selection = self.queue_listbox.curselection()
            if not selection:
                return

            index = selection[0]
            if index > 0:
                # Swap equations
                self.equation_regions[index], self.equation_regions[index - 1] = \
                    self.equation_regions[index - 1], self.equation_regions[index]

                self._update_queue_display()
                self.queue_listbox.selection_set(index - 1)
                self.update_status(f"Moved equation up")

        except Exception as e:
            self.logger.error(f"Move equation up failed: {e}")

    def _move_equation_down(self):
        """Move selected equation down in queue"""
        try:
            selection = self.queue_listbox.curselection()
            if not selection:
                return

            index = selection[0]
            if index < len(self.equation_regions) - 1:
                # Swap equations
                self.equation_regions[index], self.equation_regions[index + 1] = \
                    self.equation_regions[index + 1], self.equation_regions[index]

                self._update_queue_display()
                self.queue_listbox.selection_set(index + 1)
                self.update_status(f"Moved equation down")

        except Exception as e:
            self.logger.error(f"Move equation down failed: {e}")

    def _export_selected_equation(self):
        """Export only the selected equation"""
        try:
            selection = self.queue_listbox.curselection()
            if not selection:
                return

            equation_index = selection[0]
            if 0 <= equation_index < len(self.equation_regions):
                equation = self.equation_regions[equation_index]

                # Ask for output file
                filename = filedialog.asksaveasfilename(
                    title="Export Single Equation",
                    defaultextension=".docx",
                    filetypes=[("Word documents", "*.docx"), ("All files", "*.*")]
                )

                if filename:
                    # Convert to export format
                    equation_data = {
                        'filename': equation.filename,
                        'page_num': equation.page_num,
                        'ocr_text': equation.latex_text,  # Use LaTeX as OCR text
                        'latex_text': equation.latex_text,
                        'confidence': equation.confidence,
                        'engine_used': equation.engine_used,
                        'processing_time': equation.processing_time
                    }

                    # Export using WordExporter
                    success = self.word_exporter.export_single_equation(equation_data, filename)

                    if success:
                        messagebox.showinfo("Export Complete", f"Equation exported to {filename}")
                    else:
                        messagebox.showerror("Export Failed", "Failed to export equation")

        except Exception as e:
            self.logger.error(f"Export equation failed: {e}")
            messagebox.showerror("Export Error", f"Failed to export equation: {str(e)}")

    def batch_process(self):
        """Open batch processing dialog"""
        if not self.current_files:
            messagebox.showwarning("No Files", "Please import files first.")
            return

        BatchProcessDialog(self.root, self.current_files, self.ocr_processor, self.settings,
                          lambda results: self._handle_batch_results(results))

    def _handle_batch_results(self, results):
        """Handle batch processing results"""
        try:
            if results:
                # Add all results to equation queue
                for result in results:
                    equation = EquationRegion(
                        x=result.get('x', 0),
                        y=result.get('y', 0),
                        width=result.get('width', 0),
                        height=result.get('height', 0),
                        page_num=result.get('page_num', 0),
                        filename=result.get('filename', 'Unknown'),
                        latex_text=result.get('latex_text', ''),
                        confidence=result.get('confidence', 0.0),
                        engine_used=result.get('engine_used', 'auto'),
                        processing_time=result.get('processing_time', 0.0)
                    )
                    self.equation_regions.append(equation)

                # Update queue display
                self._update_queue_display()

                # Show summary
                messagebox.showinfo("Batch Complete",
                                  f"Batch processing completed!\n\n"
                                  f"Processed: {len(results)} equations\n"
                                  f"Total in queue: {len(self.equation_regions)}")

                self.update_status(f"Batch processing complete. Added {len(results)} equations.")
            else:
                messagebox.showinfo("Batch Complete", "No equations were found during batch processing.")

        except Exception as e:
            self.logger.error(f"Failed to handle batch results: {e}")
            messagebox.showerror("Batch Error", f"Failed to process batch results: {str(e)}")

    def open_settings(self):
        """Open application settings dialog"""
        ApplicationSettingsDialog(self.root, self.settings, self.settings_manager)

    def show_user_manual(self):
        messagebox.showinfo("User Manual", "User manual functionality")

    def show_shortcuts(self):
        messagebox.showinfo("Shortcuts", "Keyboard shortcuts functionality")

    def show_about(self):
        messagebox.showinfo("About", f"MathCapture Studio v{self.VERSION}\nResponsive Edition with AI")

    def update_status(self, message):
        if hasattr(self, 'status_label'):
            self.status_label.config(text=message)
    
    def run(self):
        """Start the application"""
        try:
            self.root.mainloop()
        except KeyboardInterrupt:
            self.root.quit()

# Dialog classes for OCR settings and performance monitoring
class OCRSettingsDialog:
    def __init__(self, parent, settings, settings_manager, ocr_processor):
        self.settings = settings
        self.settings_manager = settings_manager
        self.ocr_processor = ocr_processor
        self.parent = parent

        # Create copies for editing
        self.temp_settings = settings.copy()

        self.dialog = tk.Toplevel(parent)
        self.dialog.title("OCR Settings")
        self.dialog.geometry("600x500")
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # Center dialog
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (600 // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (500 // 2)
        self.dialog.geometry(f"600x500+{x}+{y}")

        self.create_settings_ui()

    def create_settings_ui(self):
        notebook = ttk.Notebook(self.dialog)
        notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Tesseract settings tab
        self.create_tesseract_tab(notebook)

        # Donut settings tab
        self.create_donut_tab(notebook)

        # Performance tab
        self.create_performance_tab(notebook)

        # Advanced tab
        self.create_advanced_tab(notebook)

        # Buttons
        button_frame = ttk.Frame(self.dialog)
        button_frame.pack(side=tk.BOTTOM, fill=tk.X, padx=10, pady=10)

        ttk.Button(button_frame, text="Test Tesseract", command=self.test_tesseract).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Reset to Defaults", command=self.reset_defaults).pack(side=tk.LEFT, padx=5)

        ttk.Button(button_frame, text="Cancel", command=self.dialog.destroy).pack(side=tk.RIGHT)
        ttk.Button(button_frame, text="OK", command=self.save_settings).pack(side=tk.RIGHT, padx=5)

    def create_tesseract_tab(self, notebook):
        """Create Tesseract settings tab"""
        frame = ttk.Frame(notebook)
        notebook.add(frame, text="📊 Tesseract")

        # Main container with scrollbar
        canvas = tk.Canvas(frame)
        scrollbar = ttk.Scrollbar(frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # Tesseract Path
        path_frame = ttk.LabelFrame(scrollable_frame, text="Tesseract Installation", padding=10)
        path_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(path_frame, text="Tesseract Executable Path:").pack(anchor=tk.W)
        path_entry_frame = ttk.Frame(path_frame)
        path_entry_frame.pack(fill=tk.X, pady=5)

        self.tesseract_path_var = tk.StringVar(value=self.temp_settings.get('tesseract_path', ''))
        self.tesseract_path_entry = ttk.Entry(path_entry_frame, textvariable=self.tesseract_path_var)
        self.tesseract_path_entry.pack(side=tk.LEFT, fill=tk.X, expand=True)

        ttk.Button(path_entry_frame, text="Browse", command=self.browse_tesseract_path).pack(side=tk.RIGHT, padx=(5, 0))

        # Status indicator
        self.tesseract_status_var = tk.StringVar(value="Checking...")
        self.tesseract_status_label = ttk.Label(path_frame, textvariable=self.tesseract_status_var)
        self.tesseract_status_label.pack(anchor=tk.W, pady=(5, 0))

        # Language Settings
        lang_frame = ttk.LabelFrame(scrollable_frame, text="Language Settings", padding=10)
        lang_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(lang_frame, text="OCR Language:").pack(anchor=tk.W)
        self.language_var = tk.StringVar(value=self.temp_settings.get('ocr_language', 'eng'))
        language_combo = ttk.Combobox(lang_frame, textvariable=self.language_var,
                                     values=['eng', 'eng+equ', 'chi_sim', 'chi_tra', 'fra', 'deu', 'spa', 'rus'],
                                     state="readonly")
        language_combo.pack(fill=tk.X, pady=5)

        # OCR Parameters
        params_frame = ttk.LabelFrame(scrollable_frame, text="OCR Parameters", padding=10)
        params_frame.pack(fill=tk.X, padx=5, pady=5)

        # PSM (Page Segmentation Mode)
        ttk.Label(params_frame, text="Page Segmentation Mode (PSM):").pack(anchor=tk.W)
        self.psm_var = tk.StringVar(value=str(self.temp_settings.get('tesseract_psm', 6)))
        psm_combo = ttk.Combobox(params_frame, textvariable=self.psm_var,
                                values=['3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13'],
                                state="readonly")
        psm_combo.pack(fill=tk.X, pady=5)

        # OEM (OCR Engine Mode)
        ttk.Label(params_frame, text="OCR Engine Mode (OEM):").pack(anchor=tk.W, pady=(10, 0))
        self.oem_var = tk.StringVar(value=str(self.temp_settings.get('tesseract_oem', 3)))
        oem_combo = ttk.Combobox(params_frame, textvariable=self.oem_var,
                                values=['0', '1', '2', '3'],
                                state="readonly")
        oem_combo.pack(fill=tk.X, pady=5)

        # Math Detection
        self.math_detection_var = tk.BooleanVar(value=self.temp_settings.get('math_detection_enabled', True))
        ttk.Checkbutton(params_frame, text="Enable mathematical symbol detection",
                       variable=self.math_detection_var).pack(anchor=tk.W, pady=(10, 0))

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Update Tesseract status
        self.update_tesseract_status()

    def create_donut_tab(self, notebook):
        """Create Donut settings tab"""
        frame = ttk.Frame(notebook)
        notebook.add(frame, text="🍩 Donut AI")

        # Donut availability
        avail_frame = ttk.LabelFrame(frame, text="Donut Availability", padding=10)
        avail_frame.pack(fill=tk.X, padx=5, pady=5)

        donut_available = self.ocr_processor.is_donut_available()
        status_text = "✅ Available and loaded" if donut_available else "❌ Not available"
        ttk.Label(avail_frame, text=f"Status: {status_text}").pack(anchor=tk.W)

        if not donut_available:
            ttk.Label(avail_frame, text="Install PyTorch and Transformers to enable Donut AI",
                     foreground="red").pack(anchor=tk.W, pady=(5, 0))

        # Model settings
        model_frame = ttk.LabelFrame(frame, text="Model Settings", padding=10)
        model_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(model_frame, text="Donut Model:").pack(anchor=tk.W)
        self.donut_model_var = tk.StringVar(value=self.temp_settings.get('donut_model', 'naver-clova-ix/donut-base-finetuned-cord-v2'))
        model_entry = ttk.Entry(model_frame, textvariable=self.donut_model_var)
        model_entry.pack(fill=tk.X, pady=5)

        # GPU settings
        self.use_gpu_var = tk.BooleanVar(value=self.temp_settings.get('use_gpu', True))
        ttk.Checkbutton(model_frame, text="Use GPU acceleration (if available)",
                       variable=self.use_gpu_var).pack(anchor=tk.W, pady=(10, 0))

        # Generation parameters
        gen_frame = ttk.LabelFrame(frame, text="Generation Parameters", padding=10)
        gen_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(gen_frame, text="Max Length:").pack(anchor=tk.W)
        self.donut_max_length_var = tk.StringVar(value=str(self.temp_settings.get('donut_max_length', 512)))
        ttk.Entry(gen_frame, textvariable=self.donut_max_length_var).pack(fill=tk.X, pady=5)

        ttk.Label(gen_frame, text="Number of Beams:").pack(anchor=tk.W, pady=(10, 0))
        self.donut_num_beams_var = tk.StringVar(value=str(self.temp_settings.get('donut_num_beams', 1)))
        ttk.Entry(gen_frame, textvariable=self.donut_num_beams_var).pack(fill=tk.X, pady=5)

    def create_performance_tab(self, notebook):
        """Create performance settings tab"""
        frame = ttk.Frame(notebook)
        notebook.add(frame, text="⚡ Performance")

        # Image preprocessing
        preproc_frame = ttk.LabelFrame(frame, text="Image Preprocessing", padding=10)
        preproc_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(preproc_frame, text="Enhancement Level:").pack(anchor=tk.W)
        self.enhancement_var = tk.StringVar(value=self.temp_settings.get('image_enhancement', 'medium'))
        enhancement_combo = ttk.Combobox(preproc_frame, textvariable=self.enhancement_var,
                                       values=['none', 'low', 'medium', 'high'],
                                       state="readonly")
        enhancement_combo.pack(fill=tk.X, pady=5)

        # Caching
        cache_frame = ttk.LabelFrame(frame, text="Result Caching", padding=10)
        cache_frame.pack(fill=tk.X, padx=5, pady=5)

        self.cache_enabled_var = tk.BooleanVar(value=self.temp_settings.get('enable_result_cache', True))
        ttk.Checkbutton(cache_frame, text="Enable result caching",
                       variable=self.cache_enabled_var).pack(anchor=tk.W)

        ttk.Label(cache_frame, text="Max cache size:").pack(anchor=tk.W, pady=(10, 0))
        self.cache_size_var = tk.StringVar(value=str(self.temp_settings.get('max_cache_size', 100)))
        ttk.Entry(cache_frame, textvariable=self.cache_size_var).pack(fill=tk.X, pady=5)

        # Engine selection thresholds
        threshold_frame = ttk.LabelFrame(frame, text="Auto Engine Selection", padding=10)
        threshold_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(threshold_frame, text="Donut complexity threshold:").pack(anchor=tk.W)
        self.donut_threshold_var = tk.StringVar(value=str(self.temp_settings.get('donut_complexity_threshold', 60.0)))
        ttk.Entry(threshold_frame, textvariable=self.donut_threshold_var).pack(fill=tk.X, pady=5)

        ttk.Label(threshold_frame, text="Hybrid confidence threshold:").pack(anchor=tk.W, pady=(10, 0))
        self.hybrid_threshold_var = tk.StringVar(value=str(self.temp_settings.get('hybrid_confidence_threshold', 70.0)))
        ttk.Entry(threshold_frame, textvariable=self.hybrid_threshold_var).pack(fill=tk.X, pady=5)

    def create_advanced_tab(self, notebook):
        """Create advanced settings tab"""
        frame = ttk.Frame(notebook)
        notebook.add(frame, text="🔧 Advanced")

        # Logging
        log_frame = ttk.LabelFrame(frame, text="Logging", padding=10)
        log_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(log_frame, text="Log Level:").pack(anchor=tk.W)
        self.log_level_var = tk.StringVar(value=self.temp_settings.get('log_level', 'INFO'))
        log_combo = ttk.Combobox(log_frame, textvariable=self.log_level_var,
                               values=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
                               state="readonly")
        log_combo.pack(fill=tk.X, pady=5)

        # Export settings
        export_frame = ttk.LabelFrame(frame, text="Export Settings", padding=10)
        export_frame.pack(fill=tk.X, padx=5, pady=5)

        self.include_images_var = tk.BooleanVar(value=self.temp_settings.get('export_include_images', True))
        ttk.Checkbutton(export_frame, text="Include images in Word export",
                       variable=self.include_images_var).pack(anchor=tk.W)

        self.include_confidence_var = tk.BooleanVar(value=self.temp_settings.get('export_include_confidence', False))
        ttk.Checkbutton(export_frame, text="Include confidence scores in export",
                       variable=self.include_confidence_var).pack(anchor=tk.W)

        # Reset section
        reset_frame = ttk.LabelFrame(frame, text="Reset Options", padding=10)
        reset_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Button(reset_frame, text="Clear OCR Cache", command=self.clear_cache).pack(pady=5)
        ttk.Button(reset_frame, text="Reset All Settings", command=self.reset_all_settings).pack(pady=5)

    def browse_tesseract_path(self):
        """Browse for Tesseract executable"""
        filename = filedialog.askopenfilename(
            title="Select Tesseract Executable",
            filetypes=[("Executable files", "*.exe"), ("All files", "*.*")]
        )
        if filename:
            self.tesseract_path_var.set(filename)
            self.update_tesseract_status()

    def update_tesseract_status(self):
        """Update Tesseract status indicator"""
        try:
            # Temporarily set path for testing
            old_path = pytesseract.pytesseract.tesseract_cmd
            if self.tesseract_path_var.get():
                pytesseract.pytesseract.tesseract_cmd = self.tesseract_path_var.get()

            version = pytesseract.get_tesseract_version()
            self.tesseract_status_var.set(f"✅ Found: {version}")

            # Restore old path
            pytesseract.pytesseract.tesseract_cmd = old_path

        except Exception:
            self.tesseract_status_var.set("❌ Not found or invalid path")

    def test_tesseract(self):
        """Test Tesseract installation"""
        try:
            # Create a simple test image
            test_image = Image.new('RGB', (200, 50), color='white')

            # Test OCR
            old_path = pytesseract.pytesseract.tesseract_cmd
            if self.tesseract_path_var.get():
                pytesseract.pytesseract.tesseract_cmd = self.tesseract_path_var.get()

            result = pytesseract.image_to_string(test_image)

            # Restore old path
            pytesseract.pytesseract.tesseract_cmd = old_path

            messagebox.showinfo("Test Result", "✅ Tesseract is working correctly!")

        except Exception as e:
            messagebox.showerror("Test Failed", f"❌ Tesseract test failed:\n{str(e)}")

    def clear_cache(self):
        """Clear OCR cache"""
        self.ocr_processor.clear_cache()
        messagebox.showinfo("Cache Cleared", "OCR result cache has been cleared.")

    def reset_defaults(self):
        """Reset settings to defaults"""
        if messagebox.askyesno("Reset Settings", "Reset all settings to default values?"):
            self.temp_settings = self.get_default_settings()
            self.dialog.destroy()
            # Recreate dialog with default values
            OCRSettingsDialog(self.parent, self.temp_settings, self.settings_manager, self.ocr_processor)

    def reset_all_settings(self):
        """Reset all application settings"""
        if messagebox.askyesno("Reset All", "Reset ALL application settings to defaults?\nThis cannot be undone."):
            self.temp_settings = self.get_default_settings()
            self.save_settings()
            messagebox.showinfo("Reset Complete", "All settings have been reset to defaults.")

    def get_default_settings(self):
        """Get default settings"""
        return {
            'tesseract_path': '',
            'ocr_language': 'eng',
            'tesseract_psm': 6,
            'tesseract_oem': 3,
            'math_detection_enabled': True,
            'donut_model': 'naver-clova-ix/donut-base-finetuned-cord-v2',
            'use_gpu': True,
            'donut_max_length': 512,
            'donut_num_beams': 1,
            'image_enhancement': 'medium',
            'enable_result_cache': True,
            'max_cache_size': 100,
            'donut_complexity_threshold': 60.0,
            'hybrid_confidence_threshold': 70.0,
            'log_level': 'INFO',
            'export_include_images': True,
            'export_include_confidence': False,
            'ocr_engine': 'auto'
        }

    def save_settings(self):
        """Save settings and close dialog"""
        try:
            # Update temp settings from UI
            self.temp_settings['tesseract_path'] = self.tesseract_path_var.get()
            self.temp_settings['ocr_language'] = self.language_var.get()
            self.temp_settings['tesseract_psm'] = int(self.psm_var.get())
            self.temp_settings['tesseract_oem'] = int(self.oem_var.get())
            self.temp_settings['math_detection_enabled'] = self.math_detection_var.get()

            self.temp_settings['donut_model'] = self.donut_model_var.get()
            self.temp_settings['use_gpu'] = self.use_gpu_var.get()
            self.temp_settings['donut_max_length'] = int(self.donut_max_length_var.get())
            self.temp_settings['donut_num_beams'] = int(self.donut_num_beams_var.get())

            self.temp_settings['image_enhancement'] = self.enhancement_var.get()
            self.temp_settings['enable_result_cache'] = self.cache_enabled_var.get()
            self.temp_settings['max_cache_size'] = int(self.cache_size_var.get())
            self.temp_settings['donut_complexity_threshold'] = float(self.donut_threshold_var.get())
            self.temp_settings['hybrid_confidence_threshold'] = float(self.hybrid_threshold_var.get())

            self.temp_settings['log_level'] = self.log_level_var.get()
            self.temp_settings['export_include_images'] = self.include_images_var.get()
            self.temp_settings['export_include_confidence'] = self.include_confidence_var.get()

            # Update main settings
            self.settings.update(self.temp_settings)

            # Save to file
            self.settings_manager.save_settings(self.settings)

            messagebox.showinfo("Settings Saved", "OCR settings have been saved successfully.")
            self.dialog.destroy()

        except ValueError as e:
            messagebox.showerror("Invalid Input", f"Please check your input values:\n{str(e)}")
        except Exception as e:
            messagebox.showerror("Save Error", f"Failed to save settings:\n{str(e)}")

class ApplicationSettingsDialog:
    """General application settings dialog"""

    def __init__(self, parent, settings, settings_manager):
        self.settings = settings
        self.settings_manager = settings_manager
        self.parent = parent

        # Create copies for editing
        self.temp_settings = settings.copy()

        self.dialog = tk.Toplevel(parent)
        self.dialog.title("Application Settings")
        self.dialog.geometry("500x400")
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # Center dialog
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (500 // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (400 // 2)
        self.dialog.geometry(f"500x400+{x}+{y}")

        self.create_settings_ui()

    def create_settings_ui(self):
        """Create settings UI"""
        notebook = ttk.Notebook(self.dialog)
        notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # UI Settings tab
        self.create_ui_tab(notebook)

        # Export Settings tab
        self.create_export_tab(notebook)

        # Performance tab
        self.create_performance_tab(notebook)

        # Buttons
        button_frame = ttk.Frame(self.dialog)
        button_frame.pack(side=tk.BOTTOM, fill=tk.X, padx=10, pady=10)

        ttk.Button(button_frame, text="Reset to Defaults", command=self.reset_defaults).pack(side=tk.LEFT, padx=5)

        ttk.Button(button_frame, text="Cancel", command=self.dialog.destroy).pack(side=tk.RIGHT)
        ttk.Button(button_frame, text="OK", command=self.save_settings).pack(side=tk.RIGHT, padx=5)

    def create_ui_tab(self, notebook):
        """Create UI settings tab"""
        frame = ttk.Frame(notebook)
        notebook.add(frame, text="🎨 Interface")

        # Theme settings
        theme_frame = ttk.LabelFrame(frame, text="Appearance", padding=10)
        theme_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(theme_frame, text="Theme:").pack(anchor=tk.W)
        self.theme_var = tk.StringVar(value=self.temp_settings.get('theme', 'default'))
        theme_combo = ttk.Combobox(theme_frame, textvariable=self.theme_var,
                                 values=['default', 'clam', 'alt', 'vista'],
                                 state="readonly")
        theme_combo.pack(fill=tk.X, pady=5)

        # Window settings
        window_frame = ttk.LabelFrame(frame, text="Window Behavior", padding=10)
        window_frame.pack(fill=tk.X, padx=5, pady=5)

        self.remember_size_var = tk.BooleanVar(value=self.temp_settings.get('remember_window_size', True))
        ttk.Checkbutton(window_frame, text="Remember window size and position",
                       variable=self.remember_size_var).pack(anchor=tk.W)

        self.auto_save_var = tk.BooleanVar(value=self.temp_settings.get('auto_save_projects', True))
        ttk.Checkbutton(window_frame, text="Auto-save projects",
                       variable=self.auto_save_var).pack(anchor=tk.W)

        # Toolbar settings
        toolbar_frame = ttk.LabelFrame(frame, text="Toolbar", padding=10)
        toolbar_frame.pack(fill=tk.X, padx=5, pady=5)

        self.show_tooltips_var = tk.BooleanVar(value=self.temp_settings.get('show_tooltips', True))
        ttk.Checkbutton(toolbar_frame, text="Show tooltips",
                       variable=self.show_tooltips_var).pack(anchor=tk.W)

        self.large_icons_var = tk.BooleanVar(value=self.temp_settings.get('large_toolbar_icons', False))
        ttk.Checkbutton(toolbar_frame, text="Use large toolbar icons",
                       variable=self.large_icons_var).pack(anchor=tk.W)

    def create_export_tab(self, notebook):
        """Create export settings tab"""
        frame = ttk.Frame(notebook)
        notebook.add(frame, text="📄 Export")

        # Default export settings
        default_frame = ttk.LabelFrame(frame, text="Default Export Settings", padding=10)
        default_frame.pack(fill=tk.X, padx=5, pady=5)

        self.export_images_var = tk.BooleanVar(value=self.temp_settings.get('export_include_images', True))
        ttk.Checkbutton(default_frame, text="Include equation images",
                       variable=self.export_images_var).pack(anchor=tk.W)

        self.export_latex_var = tk.BooleanVar(value=self.temp_settings.get('export_include_latex', True))
        ttk.Checkbutton(default_frame, text="Include LaTeX code",
                       variable=self.export_latex_var).pack(anchor=tk.W)

        self.export_metadata_var = tk.BooleanVar(value=self.temp_settings.get('export_include_metadata', False))
        ttk.Checkbutton(default_frame, text="Include processing metadata",
                       variable=self.export_metadata_var).pack(anchor=tk.W)

        # Document formatting
        format_frame = ttk.LabelFrame(frame, text="Document Formatting", padding=10)
        format_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(format_frame, text="Font size:").pack(anchor=tk.W)
        self.font_size_var = tk.StringVar(value=str(self.temp_settings.get('export_font_size', 12)))
        font_size_spin = ttk.Spinbox(format_frame, from_=8, to=24, textvariable=self.font_size_var)
        font_size_spin.pack(fill=tk.X, pady=5)

        ttk.Label(format_frame, text="Page margins (inches):").pack(anchor=tk.W, pady=(10, 0))
        self.margins_var = tk.StringVar(value=str(self.temp_settings.get('export_margins', 1.0)))
        margins_spin = ttk.Spinbox(format_frame, from_=0.5, to=2.0, increment=0.1, textvariable=self.margins_var)
        margins_spin.pack(fill=tk.X, pady=5)

    def create_performance_tab(self, notebook):
        """Create performance settings tab"""
        frame = ttk.Frame(notebook)
        notebook.add(frame, text="⚡ Performance")

        # Processing settings
        proc_frame = ttk.LabelFrame(frame, text="Processing", padding=10)
        proc_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(proc_frame, text="Max concurrent operations:").pack(anchor=tk.W)
        self.max_threads_var = tk.StringVar(value=str(self.temp_settings.get('max_concurrent_operations', 2)))
        threads_spin = ttk.Spinbox(proc_frame, from_=1, to=8, textvariable=self.max_threads_var)
        threads_spin.pack(fill=tk.X, pady=5)

        # Memory settings
        memory_frame = ttk.LabelFrame(frame, text="Memory Management", padding=10)
        memory_frame.pack(fill=tk.X, padx=5, pady=5)

        self.auto_cleanup_var = tk.BooleanVar(value=self.temp_settings.get('auto_cleanup_memory', True))
        ttk.Checkbutton(memory_frame, text="Automatic memory cleanup",
                       variable=self.auto_cleanup_var).pack(anchor=tk.W)

        ttk.Label(memory_frame, text="Image cache size (MB):").pack(anchor=tk.W, pady=(10, 0))
        self.cache_size_var = tk.StringVar(value=str(self.temp_settings.get('image_cache_size_mb', 100)))
        cache_spin = ttk.Spinbox(memory_frame, from_=50, to=500, increment=50, textvariable=self.cache_size_var)
        cache_spin.pack(fill=tk.X, pady=5)

        # Logging
        log_frame = ttk.LabelFrame(frame, text="Logging", padding=10)
        log_frame.pack(fill=tk.X, padx=5, pady=5)

        self.enable_logging_var = tk.BooleanVar(value=self.temp_settings.get('enable_logging', True))
        ttk.Checkbutton(log_frame, text="Enable application logging",
                       variable=self.enable_logging_var).pack(anchor=tk.W)

        ttk.Label(log_frame, text="Log level:").pack(anchor=tk.W, pady=(10, 0))
        self.log_level_var = tk.StringVar(value=self.temp_settings.get('log_level', 'INFO'))
        log_combo = ttk.Combobox(log_frame, textvariable=self.log_level_var,
                               values=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
                               state="readonly")
        log_combo.pack(fill=tk.X, pady=5)

    def reset_defaults(self):
        """Reset to default settings"""
        if messagebox.askyesno("Reset Settings", "Reset all application settings to defaults?"):
            self.temp_settings = self.get_default_settings()
            self.dialog.destroy()
            # Recreate dialog with defaults
            ApplicationSettingsDialog(self.parent, self.temp_settings, self.settings_manager)

    def get_default_settings(self):
        """Get default application settings"""
        return {
            'theme': 'default',
            'remember_window_size': True,
            'auto_save_projects': True,
            'show_tooltips': True,
            'large_toolbar_icons': False,
            'export_include_images': True,
            'export_include_latex': True,
            'export_include_metadata': False,
            'export_font_size': 12,
            'export_margins': 1.0,
            'max_concurrent_operations': 2,
            'auto_cleanup_memory': True,
            'image_cache_size_mb': 100,
            'enable_logging': True,
            'log_level': 'INFO'
        }

    def save_settings(self):
        """Save settings and close dialog"""
        try:
            # Update temp settings from UI
            self.temp_settings['theme'] = self.theme_var.get()
            self.temp_settings['remember_window_size'] = self.remember_size_var.get()
            self.temp_settings['auto_save_projects'] = self.auto_save_var.get()
            self.temp_settings['show_tooltips'] = self.show_tooltips_var.get()
            self.temp_settings['large_toolbar_icons'] = self.large_icons_var.get()

            self.temp_settings['export_include_images'] = self.export_images_var.get()
            self.temp_settings['export_include_latex'] = self.export_latex_var.get()
            self.temp_settings['export_include_metadata'] = self.export_metadata_var.get()
            self.temp_settings['export_font_size'] = int(self.font_size_var.get())
            self.temp_settings['export_margins'] = float(self.margins_var.get())

            self.temp_settings['max_concurrent_operations'] = int(self.max_threads_var.get())
            self.temp_settings['auto_cleanup_memory'] = self.auto_cleanup_var.get()
            self.temp_settings['image_cache_size_mb'] = int(self.cache_size_var.get())
            self.temp_settings['enable_logging'] = self.enable_logging_var.get()
            self.temp_settings['log_level'] = self.log_level_var.get()

            # Update main settings
            self.settings.update(self.temp_settings)

            # Save to file
            self.settings_manager.save_settings(self.settings)

            messagebox.showinfo("Settings Saved", "Application settings have been saved successfully.\nSome changes may require restarting the application.")
            self.dialog.destroy()

        except ValueError as e:
            messagebox.showerror("Invalid Input", f"Please check your input values:\n{str(e)}")
        except Exception as e:
            messagebox.showerror("Save Error", f"Failed to save settings:\n{str(e)}")

class EquationEditorDialog:
    """Dialog for editing equation properties"""

    def __init__(self, parent, equation, callback):
        self.equation = equation
        self.callback = callback

        self.dialog = tk.Toplevel(parent)
        self.dialog.title("Edit Equation")
        self.dialog.geometry("500x400")
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # Center dialog
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (500 // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (400 // 2)
        self.dialog.geometry(f"500x400+{x}+{y}")

        self.create_editor_ui()

    def create_editor_ui(self):
        """Create equation editor UI"""
        # Main frame
        main_frame = ttk.Frame(self.dialog, padding=10)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Equation info
        info_frame = ttk.LabelFrame(main_frame, text="Equation Information", padding=10)
        info_frame.pack(fill=tk.X, pady=(0, 10))

        # Source info
        ttk.Label(info_frame, text=f"Source: {self.equation.filename}").pack(anchor=tk.W)
        ttk.Label(info_frame, text=f"Page: {self.equation.page_num + 1}").pack(anchor=tk.W)
        ttk.Label(info_frame, text=f"Position: ({self.equation.x}, {self.equation.y})").pack(anchor=tk.W)
        ttk.Label(info_frame, text=f"Size: {self.equation.width} × {self.equation.height}").pack(anchor=tk.W)

        # LaTeX editor
        latex_frame = ttk.LabelFrame(main_frame, text="LaTeX Code", padding=10)
        latex_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

        self.latex_text = tk.Text(latex_frame, height=8, wrap=tk.WORD)
        latex_scrollbar = ttk.Scrollbar(latex_frame, orient=tk.VERTICAL, command=self.latex_text.yview)
        self.latex_text.config(yscrollcommand=latex_scrollbar.set)

        self.latex_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        latex_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # Insert current LaTeX
        self.latex_text.insert(1.0, self.equation.latex_text)

        # Metadata
        meta_frame = ttk.LabelFrame(main_frame, text="Processing Metadata", padding=10)
        meta_frame.pack(fill=tk.X, pady=(0, 10))

        meta_info_frame = ttk.Frame(meta_frame)
        meta_info_frame.pack(fill=tk.X)

        # Left column
        left_meta = ttk.Frame(meta_info_frame)
        left_meta.pack(side=tk.LEFT, fill=tk.X, expand=True)

        ttk.Label(left_meta, text=f"Confidence: {self.equation.confidence:.1f}%").pack(anchor=tk.W)
        ttk.Label(left_meta, text=f"Engine: {self.equation.engine_used}").pack(anchor=tk.W)

        # Right column
        right_meta = ttk.Frame(meta_info_frame)
        right_meta.pack(side=tk.RIGHT, fill=tk.X, expand=True)

        ttk.Label(right_meta, text=f"Processing Time: {self.equation.processing_time:.2f}s").pack(anchor=tk.W)

        # Buttons
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(side=tk.BOTTOM, fill=tk.X, pady=(10, 0))

        ttk.Button(button_frame, text="Preview", command=self.preview_latex).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Validate", command=self.validate_latex).pack(side=tk.LEFT, padx=5)

        ttk.Button(button_frame, text="Cancel", command=self.dialog.destroy).pack(side=tk.RIGHT)
        ttk.Button(button_frame, text="Save", command=self.save_equation).pack(side=tk.RIGHT, padx=5)

    def preview_latex(self):
        """Preview LaTeX rendering (placeholder)"""
        latex_code = self.latex_text.get(1.0, tk.END).strip()
        if latex_code:
            messagebox.showinfo("LaTeX Preview", f"LaTeX Preview:\n\n{latex_code}\n\n(Actual rendering would be implemented here)")
        else:
            messagebox.showwarning("No Content", "Please enter LaTeX code to preview.")

    def validate_latex(self):
        """Validate LaTeX syntax (basic validation)"""
        latex_code = self.latex_text.get(1.0, tk.END).strip()
        if not latex_code:
            messagebox.showwarning("No Content", "Please enter LaTeX code to validate.")
            return

        # Basic validation
        errors = []

        # Check for balanced braces
        brace_count = latex_code.count('{') - latex_code.count('}')
        if brace_count != 0:
            errors.append(f"Unbalanced braces: {abs(brace_count)} {'opening' if brace_count > 0 else 'closing'} brace(s)")

        # Check for balanced brackets
        bracket_count = latex_code.count('[') - latex_code.count(']')
        if bracket_count != 0:
            errors.append(f"Unbalanced brackets: {abs(bracket_count)} {'opening' if bracket_count > 0 else 'closing'} bracket(s)")

        # Check for balanced parentheses
        paren_count = latex_code.count('(') - latex_code.count(')')
        if paren_count != 0:
            errors.append(f"Unbalanced parentheses: {abs(paren_count)} {'opening' if paren_count > 0 else 'closing'} parenthesis/es")

        if errors:
            messagebox.showwarning("Validation Errors", "LaTeX validation found issues:\n\n" + "\n".join(errors))
        else:
            messagebox.showinfo("Validation Success", "✅ LaTeX code appears to be valid!")

    def save_equation(self):
        """Save equation changes"""
        try:
            # Get updated LaTeX
            new_latex = self.latex_text.get(1.0, tk.END).strip()

            # Create updated equation
            updated_equation = EquationRegion(
                x=self.equation.x,
                y=self.equation.y,
                width=self.equation.width,
                height=self.equation.height,
                page_num=self.equation.page_num,
                filename=self.equation.filename,
                latex_text=new_latex,
                confidence=self.equation.confidence,
                engine_used=self.equation.engine_used,
                processing_time=self.equation.processing_time
            )

            # Call callback with updated equation
            self.callback(updated_equation)

            self.dialog.destroy()

        except Exception as e:
            messagebox.showerror("Save Error", f"Failed to save equation: {str(e)}")

class BatchProcessDialog:
    """Dialog for batch processing multiple files/regions"""

    def __init__(self, parent, files, ocr_processor, settings, callback):
        self.files = files
        self.ocr_processor = ocr_processor
        self.settings = settings
        self.callback = callback
        self.processing = False
        self.results = []

        self.dialog = tk.Toplevel(parent)
        self.dialog.title("Batch Processing")
        self.dialog.geometry("600x500")
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # Center dialog
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (600 // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (500 // 2)
        self.dialog.geometry(f"600x500+{x}+{y}")

        self.create_batch_ui()

    def create_batch_ui(self):
        """Create batch processing UI"""
        main_frame = ttk.Frame(self.dialog, padding=10)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Settings frame
        settings_frame = ttk.LabelFrame(main_frame, text="Batch Settings", padding=10)
        settings_frame.pack(fill=tk.X, pady=(0, 10))

        # OCR Engine selection
        ttk.Label(settings_frame, text="OCR Engine:").pack(anchor=tk.W)
        self.engine_var = tk.StringVar(value=self.settings.get('ocr_engine', 'auto'))
        engine_combo = ttk.Combobox(settings_frame, textvariable=self.engine_var,
                                   values=['auto', 'tesseract', 'donut', 'hybrid'],
                                   state="readonly")
        engine_combo.pack(fill=tk.X, pady=5)

        # Processing options
        options_frame = ttk.Frame(settings_frame)
        options_frame.pack(fill=tk.X, pady=(10, 0))

        self.auto_detect_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(options_frame, text="Auto-detect equation regions",
                       variable=self.auto_detect_var).pack(anchor=tk.W)

        self.skip_low_confidence_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(options_frame, text="Skip low confidence results (< 50%)",
                       variable=self.skip_low_confidence_var).pack(anchor=tk.W)

        self.save_intermediate_var = tk.BooleanVar(value=False)
        ttk.Checkbutton(options_frame, text="Save intermediate results",
                       variable=self.save_intermediate_var).pack(anchor=tk.W)

        # File list
        files_frame = ttk.LabelFrame(main_frame, text="Files to Process", padding=10)
        files_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

        # File listbox with checkboxes (simplified)
        self.file_listbox = tk.Listbox(files_frame, selectmode=tk.MULTIPLE)
        file_scrollbar = ttk.Scrollbar(files_frame, orient=tk.VERTICAL, command=self.file_listbox.yview)
        self.file_listbox.config(yscrollcommand=file_scrollbar.set)

        for file_data in self.files:
            display_text = f"{file_data['basename']} ({file_data['total_pages']} pages)"
            self.file_listbox.insert(tk.END, display_text)

        # Select all files by default
        self.file_listbox.select_set(0, tk.END)

        self.file_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        file_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # Progress frame
        progress_frame = ttk.LabelFrame(main_frame, text="Progress", padding=10)
        progress_frame.pack(fill=tk.X, pady=(0, 10))

        self.progress_var = tk.IntVar()
        self.progress_bar = ttk.Progressbar(progress_frame, variable=self.progress_var,
                                          length=400, mode='determinate')
        self.progress_bar.pack(fill=tk.X, pady=5)

        self.status_var = tk.StringVar(value="Ready to process")
        self.status_label = ttk.Label(progress_frame, textvariable=self.status_var)
        self.status_label.pack(anchor=tk.W, pady=(5, 0))

        # Results frame
        results_frame = ttk.LabelFrame(main_frame, text="Results", padding=10)
        results_frame.pack(fill=tk.X, pady=(0, 10))

        self.results_text = tk.Text(results_frame, height=6, wrap=tk.WORD)
        results_scrollbar = ttk.Scrollbar(results_frame, orient=tk.VERTICAL, command=self.results_text.yview)
        self.results_text.config(yscrollcommand=results_scrollbar.set)

        self.results_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        results_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # Buttons
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(side=tk.BOTTOM, fill=tk.X, pady=(10, 0))

        self.start_button = ttk.Button(button_frame, text="Start Processing", command=self.start_processing)
        self.start_button.pack(side=tk.LEFT, padx=5)

        self.stop_button = ttk.Button(button_frame, text="Stop", command=self.stop_processing, state=tk.DISABLED)
        self.stop_button.pack(side=tk.LEFT, padx=5)

        ttk.Button(button_frame, text="Close", command=self.close_dialog).pack(side=tk.RIGHT)
        ttk.Button(button_frame, text="Save Results", command=self.save_results, state=tk.DISABLED).pack(side=tk.RIGHT, padx=5)

    def start_processing(self):
        """Start batch processing"""
        try:
            # Get selected files
            selected_indices = self.file_listbox.curselection()
            if not selected_indices:
                messagebox.showwarning("No Selection", "Please select files to process.")
                return

            selected_files = [self.files[i] for i in selected_indices]

            # Update UI state
            self.processing = True
            self.start_button.config(state=tk.DISABLED)
            self.stop_button.config(state=tk.NORMAL)
            self.results = []
            self.results_text.delete(1.0, tk.END)

            # Start processing in separate thread
            threading.Thread(target=self._process_files_async, args=(selected_files,), daemon=True).start()

        except Exception as e:
            messagebox.showerror("Processing Error", f"Failed to start processing: {str(e)}")

    def _process_files_async(self, files):
        """Process files asynchronously"""
        try:
            total_files = len(files)
            processed_equations = []

            for file_index, file_data in enumerate(files):
                if not self.processing:  # Check if stopped
                    break

                # Update progress
                file_progress = (file_index / total_files) * 100
                self.dialog.after(0, lambda p=file_progress: self.progress_var.set(p))
                self.dialog.after(0, lambda f=file_data['basename']:
                                self.status_var.set(f"Processing {f}..."))

                # Process each page
                for page_index, page_data in enumerate(file_data['pages']):
                    if not self.processing:
                        break

                    try:
                        # Update status
                        self.dialog.after(0, lambda f=file_data['basename'], p=page_index+1, t=len(file_data['pages']):
                                        self.status_var.set(f"Processing {f} - Page {p}/{t}"))

                        # Auto-detect regions or process entire page
                        if self.auto_detect_var.get():
                            regions = self._detect_equation_regions(page_data['image'])
                        else:
                            # Process entire page as one region
                            regions = [{'x': 0, 'y': 0, 'width': page_data['image'].width, 'height': page_data['image'].height}]

                        # Process each region
                        for region in regions:
                            if not self.processing:
                                break

                            try:
                                # Crop region
                                cropped_image = page_data['image'].crop((
                                    region['x'], region['y'],
                                    region['x'] + region['width'],
                                    region['y'] + region['height']
                                ))

                                # Process with OCR
                                result = self._process_region_ocr(cropped_image)

                                if result and (not self.skip_low_confidence_var.get() or result['confidence'] >= 50.0):
                                    # Add metadata
                                    result.update({
                                        'x': region['x'],
                                        'y': region['y'],
                                        'width': region['width'],
                                        'height': region['height'],
                                        'page_num': page_index,
                                        'filename': file_data['basename']
                                    })

                                    processed_equations.append(result)

                                    # Update results display
                                    self.dialog.after(0, lambda r=result: self._add_result_to_display(r))

                            except Exception as e:
                                self.dialog.after(0, lambda err=str(e):
                                                self.results_text.insert(tk.END, f"Region processing error: {err}\n"))

                    except Exception as e:
                        self.dialog.after(0, lambda err=str(e):
                                        self.results_text.insert(tk.END, f"Page processing error: {err}\n"))

            # Processing complete
            self.results = processed_equations
            self.dialog.after(0, self._processing_complete)

        except Exception as e:
            self.dialog.after(0, lambda err=str(e):
                            messagebox.showerror("Processing Error", f"Batch processing failed: {err}"))
            self.dialog.after(0, self._processing_complete)

    def _detect_equation_regions(self, image):
        """Detect equation regions in image (simplified implementation)"""
        try:
            # Use image processor to detect text regions
            from components.image_processor import ImageProcessor
            processor = ImageProcessor()

            regions = processor.detect_text_regions(image)

            # Convert to our format
            equation_regions = []
            for x, y, w, h in regions:
                # Filter by size (equations are usually larger than single characters)
                if w > 50 and h > 20:
                    equation_regions.append({
                        'x': x, 'y': y, 'width': w, 'height': h
                    })

            # If no regions found, use entire image
            if not equation_regions:
                equation_regions = [{
                    'x': 0, 'y': 0,
                    'width': image.width,
                    'height': image.height
                }]

            return equation_regions

        except Exception:
            # Fallback: use entire image
            return [{'x': 0, 'y': 0, 'width': image.width, 'height': image.height}]

    def _process_region_ocr(self, image):
        """Process a single region with OCR"""
        try:
            engine = self.engine_var.get()

            if engine == 'auto':
                result = self.ocr_processor.process_image(image)
            elif engine == 'tesseract':
                if self.ocr_processor.tesseract_processor.is_available():
                    text, confidence = self.ocr_processor.tesseract_processor.extract_text(image)
                    result = OCRResult(text, confidence, 'tesseract', 0.0)
                else:
                    return None
            elif engine == 'donut':
                if self.ocr_processor.is_donut_available():
                    text, confidence = self.ocr_processor.donut_processor.extract_text(image)
                    result = OCRResult(text, confidence, 'donut', 0.0)
                else:
                    return None
            elif engine == 'hybrid':
                result = self.ocr_processor.process_image_hybrid(image)
            else:
                return None

            if result and result.text.strip():
                return {
                    'latex_text': result.text,
                    'confidence': result.confidence,
                    'engine_used': result.engine_used,
                    'processing_time': result.processing_time
                }

            return None

        except Exception as e:
            self.dialog.after(0, lambda err=str(e):
                            self.results_text.insert(tk.END, f"OCR error: {err}\n"))
            return None

    def _add_result_to_display(self, result):
        """Add result to display"""
        try:
            display_text = f"Found: {result['latex_text'][:50]}... (Confidence: {result['confidence']:.1f}%)\n"
            self.results_text.insert(tk.END, display_text)
            self.results_text.see(tk.END)
        except Exception:
            pass

    def _processing_complete(self):
        """Handle processing completion"""
        self.processing = False
        self.start_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)
        self.progress_var.set(100)
        self.status_var.set(f"Processing complete. Found {len(self.results)} equations.")

        # Enable save button if we have results
        if self.results:
            for child in self.dialog.winfo_children():
                if isinstance(child, ttk.Frame):
                    for button in child.winfo_children():
                        if isinstance(button, ttk.Button) and button.cget('text') == 'Save Results':
                            button.config(state=tk.NORMAL)

    def stop_processing(self):
        """Stop batch processing"""
        self.processing = False
        self.status_var.set("Stopping...")

    def save_results(self):
        """Save batch processing results"""
        if not self.results:
            messagebox.showwarning("No Results", "No results to save.")
            return

        try:
            filename = filedialog.asksaveasfilename(
                title="Save Batch Results",
                defaultextension=".json",
                filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
            )

            if filename:
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(self.results, f, indent=2, ensure_ascii=False)

                messagebox.showinfo("Save Complete", f"Results saved to {filename}")

        except Exception as e:
            messagebox.showerror("Save Error", f"Failed to save results: {str(e)}")

    def close_dialog(self):
        """Close dialog and return results"""
        if self.processing:
            if messagebox.askyesno("Processing Active", "Processing is still active. Stop and close?"):
                self.processing = False
            else:
                return

        # Return results to callback
        if self.results:
            self.callback(self.results)

        self.dialog.destroy()
    def __init__(self, parent, performance_monitor):
        self.performance_monitor = performance_monitor
        
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("Performance Report")
        self.dialog.geometry("600x500")
        self.dialog.transient(parent)
        
        self.create_report_ui()
    
    def create_report_ui(self):
        # Get performance summary
        summary = self.performance_monitor.get_performance_summary()
        
        text_widget = tk.Text(self.dialog, wrap=tk.WORD, font=('Consolas', 10))
        scrollbar = ttk.Scrollbar(self.dialog, orient=tk.VERTICAL, command=text_widget.yview)
        text_widget.config(yscrollcommand=scrollbar.set)
        
        # Format and display report
        report_text = self.format_performance_report(summary)
        text_widget.insert(1.0, report_text)
        text_widget.config(state=tk.DISABLED)
        
        text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def format_performance_report(self, summary):
        return f"""
MATHCAPTURE STUDIO PERFORMANCE REPORT
Generated: {time.strftime('%Y-%m-%d %H:%M:%S')}

SESSION STATISTICS:
Total Events: {summary.get('session_info', {}).get('total_events', 0)}
Session Duration: {summary.get('session_info', {}).get('duration', 0):.1f} seconds

ENGINE PERFORMANCE:
{self.format_engine_stats(summary.get('engine_stats', {}))}

RECENT PERFORMANCE:
{self.format_recent_performance(summary.get('recent_performance', {}))}

RECOMMENDATIONS:
{self.format_recommendations(summary.get('recommendations', []))}
        """
    
    def format_engine_stats(self, engine_stats):
        result = ""
        for engine, stats in engine_stats.items():
            if stats.get('total_processed', 0) > 0:
                result += f"\n{engine.upper()}:\n"
                result += f"  Processed: {stats.get('total_processed', 0)}\n"
                result += f"  Avg Time: {stats.get('average_time', 0):.2f}s\n"
                result += f"  Avg Confidence: {stats.get('average_confidence', 0):.1f}%\n"
                result += f"  Success Rate: {stats.get('success_rate', 0)*100:.1f}%\n"
        return result
    
    def format_recent_performance(self, recent_perf):
        if recent_perf.get('events', 0) == 0:
            return "No recent activity"
        
        return f"""
Events (last 10 min): {recent_perf.get('events', 0)}
Average Time: {recent_perf.get('average_time', 0):.2f}s
Average Confidence: {recent_perf.get('average_confidence', 0):.1f}%
Success Rate: {recent_perf.get('success_rate', 0)*100:.1f}%
        """
    
    def format_recommendations(self, recommendations):
        if not recommendations:
            return "No recommendations at this time"
        
        result = ""
        for i, rec in enumerate(recommendations, 1):
            result += f"\n{i}. {rec.get('message', '')}\n"
            result += f"   Action: {rec.get('action', '')}\n"
        
        return result

class PerformanceMonitorDialog:
    def __init__(self, parent, performance_monitor, ocr_processor):
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("Real-time Performance Monitor")
        self.dialog.geometry("500x300")
        self.dialog.transient(parent)
        
        ttk.Label(self.dialog, text="Real-time performance monitoring would be implemented here").pack(pady=20)
        ttk.Button(self.dialog, text="Close", command=self.dialog.destroy).pack(pady=10)

class BenchmarkDialog:
    def __init__(self, parent, ocr_processor, performance_monitor):
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("OCR Benchmark")
        self.dialog.geometry("400x200")
        self.dialog.transient(parent)
        
        ttk.Label(self.dialog, text="OCR benchmark functionality would be implemented here").pack(pady=20)
        ttk.Button(self.dialog, text="Close", command=self.dialog.destroy).pack(pady=10)

if __name__ == "__main__":
    app = MathCaptureStudioResponsive()
    app.run()
