"""
Enhanced OCR Processor with Hybrid Donut + Tesseract System
MathCapture Studio - Responsive Edition v2.0.0

Combines traditional Tesseract OCR with modern Donut transformer for optimal
mathematical equation extraction with smart engine selection.
"""

import cv2
import numpy as np
from PIL import Image
import pytesseract
import logging
import time
import threading
from typing import Dict, Any, Optional, Tuple, List
from dataclasses import dataclass
from enum import Enum
import hashlib

# Donut imports with graceful fallback
try:
    import torch
    from transformers import DonutProcessor, VisionEncoderDecoderModel
    DONUT_AVAILABLE = True
except ImportError:
    DONUT_AVAILABLE = False
    torch = None
    DonutProcessor = None
    VisionEncoderDecoderModel = None

class OCREngine(Enum):
    """OCR Engine types"""
    TESSERACT = "tesseract"
    DONUT = "donut"
    HYBRID = "hybrid"
    AUTO = "auto"

@dataclass
class OCRResult:
    """OCR processing result with metadata"""
    text: str
    confidence: float
    processing_time: float
    engine_used: str
    latex_text: str = ""
    bounding_boxes: List[Dict] = None
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.bounding_boxes is None:
            self.bounding_boxes = []
        if self.metadata is None:
            self.metadata = {}

@dataclass
class PerformanceMetrics:
    """Performance tracking for OCR engines"""
    total_processed: int = 0
    total_time: float = 0.0
    average_time: float = 0.0
    average_confidence: float = 0.0
    success_rate: float = 0.0
    last_used: float = 0.0
    
    def update(self, processing_time: float, confidence: float, success: bool):
        self.total_processed += 1
        self.total_time += processing_time
        self.average_time = self.total_time / self.total_processed
        
        # Update rolling average confidence
        alpha = 0.1  # Smoothing factor
        self.average_confidence = (alpha * confidence + 
                                 (1 - alpha) * self.average_confidence)
        
        # Update success rate
        if success:
            self.success_rate = (self.success_rate * (self.total_processed - 1) + 1) / self.total_processed
        else:
            self.success_rate = (self.success_rate * (self.total_processed - 1)) / self.total_processed
            
        self.last_used = time.time()

class DonutOCRProcessor:
    """Donut Document Understanding Transformer processor"""
    
    def __init__(self, settings: Dict[str, Any]):
        self.settings = settings
        self.logger = logging.getLogger(__name__)
        self.model = None
        self.processor = None
        self.device = None
        self.model_loaded = False
        self.loading_lock = threading.Lock()
        
        # Performance tracking
        self.metrics = PerformanceMetrics()
        
        if DONUT_AVAILABLE:
            self._initialize_model()
        else:
            self.logger.warning("Donut dependencies not available. Install with: pip install torch transformers")
    
    def _initialize_model(self):
        """Initialize Donut model with error handling"""
        try:
            # Determine device
            if torch.cuda.is_available() and self.settings.get("use_gpu", True):
                self.device = torch.device("cuda")
                self.logger.info("Using GPU for Donut processing")
            else:
                self.device = torch.device("cpu")
                self.logger.info("Using CPU for Donut processing (slower)")
            
            # Load model in background thread to avoid blocking UI
            threading.Thread(target=self._load_model_async, daemon=True).start()
            
        except Exception as e:
            self.logger.error(f"Failed to initialize Donut: {e}")
            self.model_loaded = False
    
    def _load_model_async(self):
        """Load model asynchronously"""
        with self.loading_lock:
            try:
                model_name = self.settings.get("donut_model", "naver-clova-ix/donut-base-finetuned-cord-v2")
                
                self.logger.info(f"Loading Donut model: {model_name}")
                start_time = time.time()
                
                # Load processor and model
                self.processor = DonutProcessor.from_pretrained(model_name)
                self.model = VisionEncoderDecoderModel.from_pretrained(model_name)
                
                # Move to device
                self.model.to(self.device)
                self.model.eval()
                
                load_time = time.time() - start_time
                self.logger.info(f"Donut model loaded successfully in {load_time:.2f}s")
                self.model_loaded = True
                
            except Exception as e:
                self.logger.error(f"Failed to load Donut model: {e}")
                self.model_loaded = False
    
    def is_available(self) -> bool:
        """Check if Donut is available and loaded"""
        return DONUT_AVAILABLE and self.model_loaded
    
    def extract_text(self, image: Image.Image) -> Tuple[str, float]:
        """Extract text using Donut model"""
        if not self.is_available():
            raise RuntimeError("Donut model not available or not loaded")
        
        start_time = time.time()
        
        try:
            # Preprocess image
            processed_image = self._preprocess_image(image)
            
            # Prepare inputs
            pixel_values = self.processor(processed_image, return_tensors="pt").pixel_values
            pixel_values = pixel_values.to(self.device)
            
            # Generate text
            with torch.no_grad():
                decoder_input_ids = torch.tensor([[self.processor.tokenizer.bos_token_id]]).to(self.device)
                
                outputs = self.model.generate(
                    pixel_values,
                    decoder_input_ids=decoder_input_ids,
                    max_length=self.settings.get("donut_max_length", 512),
                    early_stopping=True,
                    pad_token_id=self.processor.tokenizer.pad_token_id,
                    eos_token_id=self.processor.tokenizer.eos_token_id,
                    use_cache=True,
                    num_beams=self.settings.get("donut_num_beams", 1),
                    bad_words_ids=[[self.processor.tokenizer.unk_token_id]],
                    return_dict_in_generate=True,
                    output_scores=True
                )
            
            # Decode output
            sequence = outputs.sequences[0]
            decoded_text = self.processor.batch_decode([sequence])[0]
            
            # Clean up the text
            text = self._clean_donut_output(decoded_text)
            
            # Calculate confidence from generation scores
            confidence = self._calculate_confidence(outputs)
            
            processing_time = time.time() - start_time
            
            # Update metrics
            success = len(text.strip()) > 0
            self.metrics.update(processing_time, confidence, success)
            
            self.logger.debug(f"Donut extraction completed in {processing_time:.2f}s with {confidence:.1f}% confidence")
            
            return text, confidence
            
        except Exception as e:
            processing_time = time.time() - start_time
            self.metrics.update(processing_time, 0.0, False)
            self.logger.error(f"Donut extraction failed: {e}")
            raise
    
    def _preprocess_image(self, image: Image.Image) -> Image.Image:
        """Preprocess image for Donut"""
        # Convert to RGB if needed
        if image.mode != 'RGB':
            image = image.convert('RGB')
        
        # Resize if too large (Donut works best with specific sizes)
        max_size = self.settings.get("donut_max_image_size", 1024)
        if max(image.size) > max_size:
            ratio = max_size / max(image.size)
            new_size = (int(image.width * ratio), int(image.height * ratio))
            image = image.resize(new_size, Image.Resampling.LANCZOS)
        
        return image
    
    def _clean_donut_output(self, text: str) -> str:
        """Clean and format Donut output"""
        # Remove special tokens
        text = text.replace("<s>", "").replace("</s>", "")
        text = text.replace("<pad>", "").replace("<unk>", "")
        
        # Clean up whitespace
        text = " ".join(text.split())
        
        return text.strip()
    
    def _calculate_confidence(self, outputs) -> float:
        """Calculate confidence score from generation outputs"""
        try:
            if hasattr(outputs, 'scores') and outputs.scores:
                # Calculate average probability of generated tokens
                scores = torch.stack(outputs.scores, dim=1)
                probs = torch.softmax(scores, dim=-1)
                max_probs = torch.max(probs, dim=-1)[0]
                avg_confidence = torch.mean(max_probs).item()
                return min(avg_confidence * 100, 100.0)
            else:
                # Fallback confidence based on text length and complexity
                return 75.0
        except Exception:
            return 70.0
    
    def get_metrics(self) -> PerformanceMetrics:
        """Get performance metrics"""
        return self.metrics

class TesseractOCRProcessor:
    """Enhanced Tesseract OCR processor with performance tracking"""

    def __init__(self, settings: Dict[str, Any]):
        self.settings = settings
        self.logger = logging.getLogger(__name__)
        self.metrics = PerformanceMetrics()
        self.tesseract_available = False

        # Configure Tesseract
        self._configure_tesseract()

    def _configure_tesseract(self):
        """Configure Tesseract settings and check availability"""
        tesseract_path = self.settings.get("tesseract_path", "")
        if tesseract_path and tesseract_path != "":
            pytesseract.pytesseract.tesseract_cmd = tesseract_path

        # Check if Tesseract is available
        try:
            version = pytesseract.get_tesseract_version()
            self.tesseract_available = True
            self.logger.info(f"Tesseract OCR found: {version}")
        except pytesseract.TesseractNotFoundError:
            self.tesseract_available = False
            self.logger.warning("Tesseract OCR not found. Please install Tesseract OCR.")
        except Exception as e:
            self.tesseract_available = False
            self.logger.error(f"Error checking Tesseract: {e}")

    def is_available(self) -> bool:
        """Check if Tesseract is available"""
        return self.tesseract_available
    
    def extract_text(self, image: Image.Image) -> Tuple[str, float]:
        """Extract text using Tesseract OCR"""
        if not self.tesseract_available:
            raise RuntimeError("Tesseract OCR is not available. Please install Tesseract OCR and ensure it's in your PATH.")

        start_time = time.time()

        try:
            # Preprocess image
            processed_image = self._preprocess_image(image)

            # Configure OCR parameters
            config = self._get_tesseract_config()

            # Extract text with confidence
            data = pytesseract.image_to_data(
                processed_image,
                config=config,
                output_type=pytesseract.Output.DICT
            )

            # Process results
            text = self._extract_text_from_data(data)
            confidence = self._calculate_confidence_from_data(data)

            processing_time = time.time() - start_time

            # Update metrics
            success = len(text.strip()) > 0 and confidence > 30
            self.metrics.update(processing_time, confidence, success)

            self.logger.debug(f"Tesseract extraction completed in {processing_time:.2f}s with {confidence:.1f}% confidence")

            return text, confidence

        except Exception as e:
            processing_time = time.time() - start_time
            self.metrics.update(processing_time, 0.0, False)
            self.logger.error(f"Tesseract extraction failed: {e}")
            raise
    
    def _preprocess_image(self, image: Image.Image) -> np.ndarray:
        """Preprocess image for better OCR results"""
        # Convert PIL to OpenCV
        img_array = np.array(image)
        if len(img_array.shape) == 3:
            img_array = cv2.cvtColor(img_array, cv2.COLOR_RGB2BGR)
        
        # Convert to grayscale
        if len(img_array.shape) == 3:
            gray = cv2.cvtColor(img_array, cv2.COLOR_BGR2GRAY)
        else:
            gray = img_array
        
        # Apply preprocessing based on settings
        enhancement = self.settings.get("image_enhancement", "medium")
        
        if enhancement == "high":
            # Aggressive preprocessing
            gray = cv2.bilateralFilter(gray, 9, 75, 75)
            gray = cv2.adaptiveThreshold(gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2)
        elif enhancement == "medium":
            # Moderate preprocessing
            gray = cv2.medianBlur(gray, 3)
            _, gray = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        # "low" or "none" - minimal preprocessing
        
        return gray
    
    def _get_tesseract_config(self) -> str:
        """Get Tesseract configuration string"""
        psm = self.settings.get("tesseract_psm", 6)
        oem = self.settings.get("tesseract_oem", 3)
        lang = self.settings.get("ocr_language", "eng")
        
        config = f"--oem {oem} --psm {psm} -l {lang}"
        
        # Add math-specific configurations
        if self.settings.get("math_detection_enabled", True):
            config += " -c tessedit_char_whitelist=0123456789+-*/=()[]{}^_abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ\\αβγδεζηθικλμνξοπρστυφχψω∫∑∏√∞≤≥≠±∂∇"
        
        return config
    
    def _extract_text_from_data(self, data: Dict) -> str:
        """Extract text from Tesseract data"""
        words = []
        confidences = data['conf']
        texts = data['text']
        
        for i, conf in enumerate(confidences):
            if conf > 0:  # Only include confident detections
                text = texts[i].strip()
                if text:
                    words.append(text)
        
        return ' '.join(words)
    
    def _calculate_confidence_from_data(self, data: Dict) -> float:
        """Calculate average confidence from Tesseract data"""
        confidences = [conf for conf in data['conf'] if conf > 0]
        if confidences:
            return sum(confidences) / len(confidences)
        return 0.0
    
    def get_metrics(self) -> PerformanceMetrics:
        """Get performance metrics"""
        return self.metrics

class ComplexityAnalyzer:
    """Analyze image complexity to determine optimal OCR engine"""
    
    def __init__(self, settings: Dict[str, Any]):
        self.settings = settings
        self.logger = logging.getLogger(__name__)
    
    def analyze_complexity(self, image: Image.Image) -> Dict[str, Any]:
        """Analyze image complexity and recommend OCR engine"""
        try:
            # Convert to numpy array
            img_array = np.array(image.convert('L'))  # Grayscale
            
            # Calculate various complexity metrics
            metrics = {
                'image_size': image.size,
                'pixel_count': img_array.size,
                'mean_intensity': float(np.mean(img_array)),
                'std_intensity': float(np.std(img_array)),
                'edge_density': self._calculate_edge_density(img_array),
                'text_density': self._estimate_text_density(img_array),
                'symbol_complexity': self._estimate_symbol_complexity(img_array),
                'aspect_ratio': image.width / image.height,
            }
            
            # Determine complexity score (0-100)
            complexity_score = self._calculate_complexity_score(metrics)
            metrics['complexity_score'] = complexity_score
            
            # Recommend engine
            recommended_engine = self._recommend_engine(metrics)
            metrics['recommended_engine'] = recommended_engine
            
            return metrics
            
        except Exception as e:
            self.logger.error(f"Complexity analysis failed: {e}")
            return {
                'complexity_score': 50.0,
                'recommended_engine': OCREngine.TESSERACT,
                'error': str(e)
            }
    
    def _calculate_edge_density(self, img_array: np.ndarray) -> float:
        """Calculate edge density using Canny edge detection"""
        try:
            edges = cv2.Canny(img_array, 50, 150)
            edge_pixels = np.sum(edges > 0)
            total_pixels = img_array.size
            return float(edge_pixels / total_pixels)
        except:
            return 0.1
    
    def _estimate_text_density(self, img_array: np.ndarray) -> float:
        """Estimate text density in the image"""
        try:
            # Simple text density estimation based on connected components
            _, binary = cv2.threshold(img_array, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)
            num_labels, labels, stats, centroids = cv2.connectedComponentsWithStats(binary)
            
            # Filter components by size (likely text)
            text_components = 0
            for i in range(1, num_labels):
                area = stats[i, cv2.CC_STAT_AREA]
                if 10 < area < 1000:  # Typical text component size
                    text_components += 1
            
            return float(text_components / img_array.size * 10000)  # Normalized
        except:
            return 0.1
    
    def _estimate_symbol_complexity(self, img_array: np.ndarray) -> float:
        """Estimate mathematical symbol complexity"""
        try:
            # Look for complex mathematical symbols (curves, fractions, etc.)
            # This is a simplified heuristic
            
            # Calculate local variance to detect complex shapes
            kernel = np.ones((5, 5), np.float32) / 25
            local_mean = cv2.filter2D(img_array.astype(np.float32), -1, kernel)
            local_variance = cv2.filter2D((img_array.astype(np.float32) - local_mean) ** 2, -1, kernel)
            
            high_variance_pixels = np.sum(local_variance > np.percentile(local_variance, 90))
            return float(high_variance_pixels / img_array.size)
        except:
            return 0.1
    
    def _calculate_complexity_score(self, metrics: Dict[str, Any]) -> float:
        """Calculate overall complexity score"""
        try:
            score = 0.0
            
            # Edge density contribution (0-30 points)
            edge_density = metrics.get('edge_density', 0.1)
            score += min(edge_density * 300, 30)
            
            # Text density contribution (0-25 points)
            text_density = metrics.get('text_density', 0.1)
            score += min(text_density * 250, 25)
            
            # Symbol complexity contribution (0-25 points)
            symbol_complexity = metrics.get('symbol_complexity', 0.1)
            score += min(symbol_complexity * 250, 25)
            
            # Image size contribution (0-10 points)
            pixel_count = metrics.get('pixel_count', 100000)
            if pixel_count > 500000:  # Large images are more complex
                score += 10
            elif pixel_count > 200000:
                score += 5
            
            # Intensity variation contribution (0-10 points)
            std_intensity = metrics.get('std_intensity', 50)
            if std_intensity > 60:  # High variation suggests complex content
                score += 10
            elif std_intensity > 40:
                score += 5
            
            return min(score, 100.0)
            
        except Exception as e:
            self.logger.error(f"Complexity score calculation failed: {e}")
            return 50.0
    
    def _recommend_engine(self, metrics: Dict[str, Any]) -> OCREngine:
        """Recommend OCR engine based on complexity metrics"""
        complexity_score = metrics.get('complexity_score', 50.0)
        
        # Thresholds from settings
        donut_threshold = self.settings.get('donut_complexity_threshold', 60.0)
        hybrid_threshold = self.settings.get('hybrid_complexity_threshold', 40.0)
        
        if complexity_score >= donut_threshold:
            return OCREngine.DONUT
        elif complexity_score >= hybrid_threshold:
            return OCREngine.HYBRID
        else:
            return OCREngine.TESSERACT

class MathOCRProcessor:
    """Enhanced OCR processor with hybrid Donut + Tesseract system"""
    
    def __init__(self, settings: Dict[str, Any]):
        self.settings = settings
        self.logger = logging.getLogger(__name__)
        
        # Initialize processors
        self.tesseract_processor = TesseractOCRProcessor(settings)
        self.donut_processor = DonutOCRProcessor(settings) if DONUT_AVAILABLE else None
        self.complexity_analyzer = ComplexityAnalyzer(settings)
        
        # Performance tracking
        self.session_metrics = {
            'total_processed': 0,
            'tesseract_used': 0,
            'donut_used': 0,
            'hybrid_decisions': 0,
            'total_time': 0.0,
            'average_confidence': 0.0
        }
        
        # Cache for repeated images
        self.result_cache = {}
        self.cache_enabled = settings.get('enable_result_cache', True)
        self.max_cache_size = settings.get('max_cache_size', 100)
        
        self.logger.info(f"MathOCRProcessor initialized. Donut available: {DONUT_AVAILABLE}")
    
    def extract_text(self, image: Image.Image, engine: OCREngine = None) -> Tuple[str, float]:
        """Extract text using specified or auto-selected engine"""
        start_time = time.time()
        
        try:
            # Check cache first
            if self.cache_enabled:
                cache_key = self._get_image_hash(image)
                if cache_key in self.result_cache:
                    cached_result = self.result_cache[cache_key]
                    self.logger.debug("Using cached OCR result")
                    return cached_result['text'], cached_result['confidence']
            
            # Determine engine to use
            if engine is None:
                engine = OCREngine(self.settings.get('ocr_engine', 'auto'))
            
            # Process based on engine selection
            if engine == OCREngine.AUTO:
                result = self._auto_select_and_process(image)
            elif engine == OCREngine.HYBRID:
                result = self._hybrid_process(image)
            elif engine == OCREngine.DONUT:
                result = self._process_with_donut(image)
            elif engine == OCREngine.TESSERACT:
                result = self._process_with_tesseract(image)
            else:
                raise ValueError(f"Unknown OCR engine: {engine}")
            
            # Cache result
            if self.cache_enabled and cache_key:
                self._cache_result(cache_key, result)
            
            # Update session metrics
            self._update_session_metrics(result, time.time() - start_time)
            
            return result.text, result.confidence
            
        except Exception as e:
            self.logger.error(f"OCR processing failed: {e}")
            # Return empty result
            return "", 0.0
    
    def _auto_select_and_process(self, image: Image.Image) -> OCRResult:
        """Automatically select and process with optimal engine"""
        # Analyze complexity
        complexity_metrics = self.complexity_analyzer.analyze_complexity(image)
        recommended_engine = complexity_metrics.get('recommended_engine', OCREngine.TESSERACT)
        
        self.logger.debug(f"Complexity score: {complexity_metrics.get('complexity_score', 0):.1f}, "
                         f"Recommended engine: {recommended_engine.value}")
        
        # Process with recommended engine
        if recommended_engine == OCREngine.DONUT and self.donut_processor and self.donut_processor.is_available():
            return self._process_with_donut(image, complexity_metrics)
        elif recommended_engine == OCREngine.HYBRID:
            return self._hybrid_process(image, complexity_metrics)
        else:
            return self._process_with_tesseract(image, complexity_metrics)
    
    def _hybrid_process(self, image: Image.Image, complexity_metrics: Dict = None) -> OCRResult:
        """Process with hybrid approach - try both engines and select best result"""
        results = []
        
        # Try Tesseract first (faster)
        try:
            tesseract_result = self._process_with_tesseract(image, complexity_metrics)
            results.append(tesseract_result)
        except Exception as e:
            self.logger.warning(f"Tesseract processing failed in hybrid mode: {e}")
        
        # Try Donut if available and Tesseract confidence is low
        if (self.donut_processor and self.donut_processor.is_available() and 
            (not results or results[0].confidence < self.settings.get('hybrid_confidence_threshold', 70))):
            try:
                donut_result = self._process_with_donut(image, complexity_metrics)
                results.append(donut_result)
            except Exception as e:
                self.logger.warning(f"Donut processing failed in hybrid mode: {e}")
        
        # Select best result
        if not results:
            raise RuntimeError("Both OCR engines failed")
        
        # Choose result with highest confidence
        best_result = max(results, key=lambda r: r.confidence)
        best_result.engine_used = "hybrid"
        best_result.metadata['hybrid_results'] = [
            {'engine': r.engine_used, 'confidence': r.confidence, 'text_length': len(r.text)}
            for r in results
        ]
        
        self.session_metrics['hybrid_decisions'] += 1
        return best_result
    
    def _process_with_tesseract(self, image: Image.Image, complexity_metrics: Dict = None) -> OCRResult:
        """Process with Tesseract OCR"""
        start_time = time.time()
        
        text, confidence = self.tesseract_processor.extract_text(image)
        latex_text = self.convert_to_latex(text)
        
        processing_time = time.time() - start_time
        
        result = OCRResult(
            text=text,
            confidence=confidence,
            processing_time=processing_time,
            engine_used="tesseract",
            latex_text=latex_text,
            metadata={
                'complexity_metrics': complexity_metrics,
                'tesseract_metrics': self.tesseract_processor.get_metrics().__dict__
            }
        )
        
        self.session_metrics['tesseract_used'] += 1
        return result
    
    def _process_with_donut(self, image: Image.Image, complexity_metrics: Dict = None) -> OCRResult:
        """Process with Donut transformer"""
        if not self.donut_processor or not self.donut_processor.is_available():
            raise RuntimeError("Donut processor not available")
        
        start_time = time.time()
        
        text, confidence = self.donut_processor.extract_text(image)
        latex_text = self.convert_to_latex(text)
        
        processing_time = time.time() - start_time
        
        result = OCRResult(
            text=text,
            confidence=confidence,
            processing_time=processing_time,
            engine_used="donut",
            latex_text=latex_text,
            metadata={
                'complexity_metrics': complexity_metrics,
                'donut_metrics': self.donut_processor.get_metrics().__dict__
            }
        )
        
        self.session_metrics['donut_used'] += 1
        return result
    
    def convert_to_latex(self, text: str) -> str:
        """Convert OCR text to LaTeX format with enhanced mathematical notation"""
        if not text or not text.strip():
            return ""
        
        latex_text = text
        
        # Enhanced mathematical symbol replacements
        replacements = {
            # Greek letters
            'alpha': r'\alpha', 'beta': r'\beta', 'gamma': r'\gamma', 'delta': r'\delta',
            'epsilon': r'\epsilon', 'zeta': r'\zeta', 'eta': r'\eta', 'theta': r'\theta',
            'iota': r'\iota', 'kappa': r'\kappa', 'lambda': r'\lambda', 'mu': r'\mu',
            'nu': r'\nu', 'xi': r'\xi', 'omicron': r'\omicron', 'pi': r'\pi',
            'rho': r'\rho', 'sigma': r'\sigma', 'tau': r'\tau', 'upsilon': r'\upsilon',
            'phi': r'\phi', 'chi': r'\chi', 'psi': r'\psi', 'omega': r'\omega',
            
            # Mathematical operators
            '±': r'\pm', '∓': r'\mp', '×': r'\times', '÷': r'\div',
            '≤': r'\leq', '≥': r'\geq', '≠': r'\neq', '≈': r'\approx',
            '∞': r'\infty', '∂': r'\partial', '∇': r'\nabla',
            '∫': r'\int', '∑': r'\sum', '∏': r'\prod',
            '√': r'\sqrt', '∛': r'\sqrt[3]', '∜': r'\sqrt[4]',
            
            # Arrows
            '→': r'\rightarrow', '←': r'\leftarrow', '↔': r'\leftrightarrow',
            '⇒': r'\Rightarrow', '⇐': r'\Leftarrow', '⇔': r'\Leftrightarrow',
            
            # Set theory
            '∈': r'\in', '∉': r'\notin', '⊂': r'\subset', '⊃': r'\supset',
            '⊆': r'\subseteq', '⊇': r'\supseteq', '∪': r'\cup', '∩': r'\cap',
            '∅': r'\emptyset', '∀': r'\forall', '∃': r'\exists',
            
            # Logic
            '∧': r'\land', '∨': r'\lor', '¬': r'\lnot', '⊕': r'\oplus',
        }
        
        # Apply replacements
        for symbol, latex in replacements.items():
            latex_text = latex_text.replace(symbol, latex)
        
        # Handle fractions (simple pattern matching)
        import re
        
        # Pattern for simple fractions like "a/b" -> "\frac{a}{b}"
        fraction_pattern = r'(\w+|$$[^)]+$$)/(\w+|$$[^)]+$$)'
        latex_text = re.sub(fraction_pattern, r'\\frac{\1}{\2}', latex_text)
        
        # Handle superscripts (simple pattern)
        superscript_pattern = r'(\w+)\^(\w+|$$[^)]+$$)'
        latex_text = re.sub(superscript_pattern, r'\1^{\2}', latex_text)
        
        # Handle subscripts (simple pattern)
        subscript_pattern = r'(\w+)_(\w+|$$[^)]+$$)'
        latex_text = re.sub(subscript_pattern, r'\1_{\2}', latex_text)
        
        # Clean up extra spaces
        latex_text = ' '.join(latex_text.split())
        
        return latex_text
    
    def _get_image_hash(self, image: Image.Image) -> str:
        """Generate hash for image caching"""
        try:
            # Convert image to bytes and hash
            img_bytes = image.tobytes()
            return hashlib.md5(img_bytes).hexdigest()
        except:
            return None
    
    def _cache_result(self, cache_key: str, result: OCRResult):
        """Cache OCR result"""
        if len(self.result_cache) >= self.max_cache_size:
            # Remove oldest entry
            oldest_key = next(iter(self.result_cache))
            del self.result_cache[oldest_key]
        
        self.result_cache[cache_key] = {
            'text': result.text,
            'confidence': result.confidence,
            'latex_text': result.latex_text,
            'engine_used': result.engine_used,
            'timestamp': time.time()
        }
    
    def _update_session_metrics(self, result: OCRResult, total_time: float):
        """Update session performance metrics"""
        self.session_metrics['total_processed'] += 1
        self.session_metrics['total_time'] += total_time
        
        # Update rolling average confidence
        alpha = 0.1
        self.session_metrics['average_confidence'] = (
            alpha * result.confidence + 
            (1 - alpha) * self.session_metrics['average_confidence']
        )
    
    def get_performance_report(self) -> Dict[str, Any]:
        """Get comprehensive performance report"""
        report = {
            'session_metrics': self.session_metrics.copy(),
            'tesseract_metrics': self.tesseract_processor.get_metrics().__dict__,
            'cache_stats': {
                'enabled': self.cache_enabled,
                'size': len(self.result_cache),
                'max_size': self.max_cache_size
            },
            'donut_available': DONUT_AVAILABLE,
            'engines_available': {
                'tesseract': True,
                'donut': self.donut_processor.is_available() if self.donut_processor else False
            }
        }
        
        if self.donut_processor:
            report['donut_metrics'] = self.donut_processor.get_metrics().__dict__
        
        # Calculate derived metrics
        total_processed = self.session_metrics['total_processed']
        if total_processed > 0:
            report['session_metrics']['average_processing_time'] = (
                self.session_metrics['total_time'] / total_processed
            )
            report['session_metrics']['tesseract_usage_percent'] = (
                self.session_metrics['tesseract_used'] / total_processed * 100
            )
            report['session_metrics']['donut_usage_percent'] = (
                self.session_metrics['donut_used'] / total_processed * 100
            )
        
        return report
    
    def clear_cache(self):
        """Clear result cache"""
        self.result_cache.clear()
        self.logger.info("OCR result cache cleared")
    
    def is_donut_available(self) -> bool:
        """Check if Donut is available"""
        return self.donut_processor and self.donut_processor.is_available()
    
    def get_engine_status(self) -> Dict[str, Any]:
        """Get status of all OCR engines"""
        return {
            'tesseract': {
                'available': self.tesseract_processor.is_available(),
                'status': 'ready' if self.tesseract_processor.is_available() else 'not_installed',
                'metrics': self.tesseract_processor.get_metrics().__dict__
            },
            'donut': {
                'available': DONUT_AVAILABLE,
                'loaded': self.donut_processor.is_available() if self.donut_processor else False,
                'status': 'ready' if (self.donut_processor and self.donut_processor.is_available()) else 'not_available',
                'metrics': self.donut_processor.get_metrics().__dict__ if self.donut_processor else {}
            }
        }
